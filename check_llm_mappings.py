import pandas as pd

# Read the Excel file
df = pd.read_excel('vaccines.xlsx', sheet_name='Required Mapping')

# Check for LLM auto-mapped records
llm_mapped = df[df['Mapping Status'] == 'llm_auto_mapped']
print(f'LLM Auto-mapped records: {len(llm_mapped)}')
print()

if len(llm_mapped) > 0:
    for i, (idx, row) in enumerate(llm_mapped.iterrows(), 1):
        print(f'{i}. {row["Medtech Vaccine Code"]} - {row["Medtech Vaccine Description"]}')
        print(f'   → {row["IndiciVaccineName"]} ({row["NIR / Indici Vaccine Code"]})')
        print(f'   Comments: {row["Comments"]}')
        print()
else:
    print('No LLM auto-mapped records found')

# Also check for any records with "LLM" in comments
llm_comments = df[df['Comments'].str.contains('LLM', na=False)]
print(f'\nRecords with LLM in comments: {len(llm_comments)}')

if len(llm_comments) > 0:
    for i, (idx, row) in enumerate(llm_comments.iterrows(), 1):
        print(f'{i}. {row["Medtech Vaccine Code"]} - {row["Medtech Vaccine Description"]}')
        print(f'   Status: {row["Mapping Status"]}')
        print(f'   Comments: {row["Comments"][:100]}...')
        print()
