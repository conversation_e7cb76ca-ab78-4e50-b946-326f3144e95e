#!/usr/bin/env python3
"""
Fully Automated LLM Vaccine Mapping - NO HUMAN INTERVENTION
This system automatically maps ALL vaccines using LLM without requiring human review.
"""

import pandas as pd
import numpy as np
import logging
import json
import time
from datetime import datetime
import os
from dotenv import load_dotenv
from openai import OpenAI

from vaccine_mapper import VaccineMapper
from config import *
from utils import *

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('automated_mapping.log')
    ]
)
logger = logging.getLogger(__name__)

class FullyAutomatedLLMMapper:
    """Fully automated LLM-based vaccine mapper with no human intervention."""
    
    def __init__(self):
        """Initialize the automated mapper."""
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Please check your .env file.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.mapper = None
        self.indici_database = []
        
    def load_data(self):
        """Load and prepare data."""
        logger.info("Loading vaccine data...")
        
        self.mapper = VaccineMapper(EXCEL_FILE)
        if not self.mapper.load_data():
            raise Exception("Failed to load vaccine data")
        
        self.mapper.clean_and_normalize_data()
        self.mapper.initialize_required_mapping()
        
        # Prepare Indici database for LLM
        self.indici_database = []
        for _, row in self.mapper.indici_df.iterrows():
            vaccine_record = {
                'vaccine_code': str(row[INDICI_COLUMNS['vaccine_code']]),
                'vaccine_name': str(row[INDICI_COLUMNS['vaccine_name']]),
                'brand': str(row[INDICI_COLUMNS['brand']]) if pd.notna(row[INDICI_COLUMNS['brand']]) else '',
                'long_description': str(row[INDICI_COLUMNS['long_description']]) if pd.notna(row[INDICI_COLUMNS['long_description']]) else ''
            }
            self.indici_database.append(vaccine_record)
        
        logger.info(f"Loaded {len(self.indici_database)} standard vaccines for matching")
        
    def create_llm_prompt(self, legacy_vaccine):
        """Create optimized prompt for automated mapping."""
        
        prompt = f"""You are an expert vaccine mapping system. Your task is to automatically find the best match for a legacy vaccine from the standard database.

LEGACY VACCINE TO MAP:
Code: {legacy_vaccine.get('code', 'N/A')}
Description: {legacy_vaccine.get('description', 'N/A')}

STANDARD VACCINE DATABASE:
"""
        
        # Add database in compact format
        for i, vaccine in enumerate(self.indici_database, 1):
            prompt += f"{i}. {vaccine['vaccine_code']}|{vaccine['vaccine_name']}|{vaccine['brand']}|{vaccine['long_description']}\n"
        
        prompt += """
INSTRUCTIONS:
1. Find the BEST match from the database above
2. Consider medical equivalence, not just text similarity
3. Match based on vaccine type, components, and clinical use
4. If multiple matches exist, choose the most specific/complete one
5. If no good match exists, return null
6. Be consistent - same legacy vaccine should always map to same result

RESPONSE FORMAT (JSON only):
{
    "match_found": true/false,
    "vaccine_code": "exact code from database or null",
    "vaccine_name": "exact name from database or null", 
    "confidence": 0-100,
    "rationale": "brief explanation"
}

MATCHING CRITERIA:
- 90-100: Perfect match (same vaccine, different naming)
- 80-89: Very good match (same vaccine type/components)
- 70-79: Good match (similar vaccine family)
- 60-69: Moderate match (related vaccines)
- Below 60: No match (return null)

AUTO-ACCEPT THRESHOLD: 70% or higher

Respond with JSON only, no other text."""

        return prompt
    
    def get_llm_mapping(self, legacy_vaccine, retry_count=3):
        """Get automated mapping from LLM."""
        
        for attempt in range(retry_count):
            try:
                prompt = self.create_llm_prompt(legacy_vaccine)
                
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are an automated vaccine mapping system. Always respond with valid JSON only."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=500,
                    temperature=0.1
                )
                
                # Parse response
                response_text = response.choices[0].message.content.strip()
                
                # Clean JSON if needed
                if response_text.startswith('```json'):
                    response_text = response_text.replace('```json', '').replace('```', '').strip()
                
                result = json.loads(response_text)
                
                # Validate response
                if 'match_found' in result and 'confidence' in result:
                    return result
                else:
                    logger.warning(f"Invalid LLM response format: {result}")
                    
            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error (attempt {attempt + 1}): {e}")
                logger.warning(f"Raw response: {response_text}")
                
            except Exception as e:
                logger.warning(f"LLM request error (attempt {attempt + 1}): {e}")
                
            # Wait before retry
            if attempt < retry_count - 1:
                time.sleep(1)
        
        # Return no match if all attempts failed
        return {
            "match_found": False,
            "vaccine_code": None,
            "vaccine_name": None,
            "confidence": 0,
            "rationale": "LLM processing failed after retries"
        }
    
    def process_all_vaccines(self):
        """Process all vaccines automatically."""
        
        logger.info("Starting fully automated vaccine mapping...")
        
        # Apply existing overrides first
        self.mapper.apply_overrides()
        
        # Apply exact matches
        self.mapper.exact_matching_pass()
        
        # Get all unmapped vaccines (skip fuzzy matches - we'll remap them)
        all_mask = (
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna()) |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == STATUS_FUZZY_HIGH) |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == STATUS_NEEDS_REVIEW)
        )
        
        unmapped_vaccines = self.mapper.required_mapping_df[all_mask]
        total_to_process = len(unmapped_vaccines)
        
        logger.info(f"Processing {total_to_process} vaccines with LLM...")
        
        # Counters
        auto_mapped = 0
        no_match = 0
        failed = 0
        
        for idx, (row_idx, row) in enumerate(unmapped_vaccines.iterrows(), 1):
            legacy_vaccine = {
                'code': row[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                'description': row[REQUIRED_MAPPING_COLUMNS['legacy_description']]
            }
            
            logger.info(f"Processing {idx}/{total_to_process}: {legacy_vaccine['code']} - {legacy_vaccine['description'][:50]}...")
            
            # Get LLM mapping
            result = self.get_llm_mapping(legacy_vaccine)
            
            if result['match_found'] and result['confidence'] >= 70:
                # Auto-map with high confidence
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = result['vaccine_code']
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = result['vaccine_name']
                
                # Find full description
                full_vaccine = next((v for v in self.indici_database if v['vaccine_code'] == result['vaccine_code']), None)
                if full_vaccine:
                    self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = full_vaccine['long_description']
                
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = "llm_auto_mapped"
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"Auto-mapped by LLM (confidence: {result['confidence']}%) - {result['rationale']}"
                
                auto_mapped += 1
                logger.info(f"✅ Auto-mapped to {result['vaccine_name']} (confidence: {result['confidence']}%)")
                
            elif result['match_found'] and result['confidence'] < 70:
                # Low confidence - mark as no match
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"LLM found potential match but low confidence ({result['confidence']}%) - {result['rationale']}"
                
                no_match += 1
                logger.info(f"❌ Low confidence match ({result['confidence']}%) - marked as no match")
                
            else:
                # No match found
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"LLM found no suitable match - {result['rationale']}"
                
                no_match += 1
                logger.info(f"❌ No match found")
            
            # Small delay to avoid rate limits
            time.sleep(0.5)
        
        logger.info(f"Completed processing: {auto_mapped} auto-mapped, {no_match} no match, {failed} failed")
        return auto_mapped, no_match, failed
    
    def save_results(self):
        """Save all results to Excel."""
        logger.info("Saving results to Excel...")
        return self.mapper.save_results()
    
    def generate_summary(self):
        """Generate final summary."""
        summary = self.mapper.get_mapping_summary()
        total = len(self.mapper.required_mapping_df)
        
        print("\n" + "=" * 60)
        print("🤖 FULLY AUTOMATED LLM MAPPING COMPLETED")
        print("=" * 60)
        print(f"Total vaccines processed: {total}")
        print()
        
        for status, count in summary.items():
            percentage = (count / total * 100) if total > 0 else 0
            status_display = {
                STATUS_EXACT: "✅ Exact matches",
                "llm_auto_mapped": "🤖 LLM auto-mapped",
                STATUS_NO_MATCH: "❌ No matches found",
                STATUS_MANUAL_CONFIRMED: "👤 Previously confirmed",
                STATUS_MANUAL_OVERRIDE: "🔧 Manual overrides",
                'unmapped': "❓ Still unmapped"
            }.get(status, f"📊 {status}")
            
            print(f"{status_display}: {count} ({percentage:.1f}%)")
        
        # Calculate automation rate
        automated = summary.get(STATUS_EXACT, 0) + summary.get("llm_auto_mapped", 0)
        automation_rate = (automated / total * 100) if total > 0 else 0
        
        print(f"\n🎯 AUTOMATION ACHIEVED: {automated}/{total} vaccines ({automation_rate:.1f}%)")
        print("🎉 NO HUMAN INTERVENTION REQUIRED!")
        print("=" * 60)

def main():
    """Main function."""
    
    print("🤖 FULLY AUTOMATED LLM VACCINE MAPPING")
    print("=" * 60)
    print("This system will automatically map ALL vaccines using AI")
    print("NO human intervention required!")
    print()
    
    try:
        # Initialize mapper
        mapper = FullyAutomatedLLMMapper()
        
        # Load data
        mapper.load_data()
        
        # Process all vaccines
        auto_mapped, no_match, failed = mapper.process_all_vaccines()
        
        # Save results
        if mapper.save_results():
            print("✅ Results saved successfully!")
        else:
            print("❌ Failed to save results")
            return
        
        # Generate summary
        mapper.generate_summary()
        
        print(f"\n📁 Results saved to: {EXCEL_FILE}")
        print("📋 Check the 'Required Mapping' sheet for all mappings")
        
    except Exception as e:
        logger.error(f"Automation failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
