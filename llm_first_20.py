#!/usr/bin/env python3
"""
Pure LLM mapping for first 20 records only.
Clean approach with no fuzzy logic or rule-based interference.
"""

import pandas as pd
import numpy as np
import logging
import json
import time
from datetime import datetime
import os
from dotenv import load_dotenv
from openai import OpenAI

from vaccine_mapper import VaccineMapper
from config import *
from utils import *

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PureLLMMapper:
    """Pure LLM-based vaccine mapper for first 20 records."""
    
    def __init__(self):
        """Initialize the LLM mapper."""
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Please check your .env file.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.mapper = None
        self.indici_database = []
        
    def load_data(self):
        """Load and prepare data."""
        logger.info("Loading vaccine data...")
        
        self.mapper = VaccineMapper(EXCEL_FILE)
        if not self.mapper.load_data():
            raise Exception("Failed to load vaccine data")
        
        self.mapper.clean_and_normalize_data()
        self.mapper.initialize_required_mapping()
        
        # Prepare Indici database for LLM
        self.indici_database = []
        for _, row in self.mapper.indici_df.iterrows():
            vaccine_record = {
                'vaccine_code': str(row[INDICI_COLUMNS['vaccine_code']]),
                'vaccine_name': str(row[INDICI_COLUMNS['vaccine_name']]),
                'brand': str(row[INDICI_COLUMNS['brand']]) if pd.notna(row[INDICI_COLUMNS['brand']]) else '',
                'long_description': str(row[INDICI_COLUMNS['long_description']]) if pd.notna(row[INDICI_COLUMNS['long_description']]) else ''
            }
            self.indici_database.append(vaccine_record)
        
        logger.info(f"Loaded {len(self.indici_database)} standard vaccines for matching")
        
    def create_llm_prompt(self, legacy_vaccine):
        """Create optimized prompt for LLM mapping."""
        
        prompt = f"""You are an expert medical vaccine mapping system. Find the best match for this legacy vaccine from the standard database.

LEGACY VACCINE TO MAP:
Code: {legacy_vaccine.get('code', 'N/A')}
Description: {legacy_vaccine.get('description', 'N/A')}

STANDARD VACCINE DATABASE:
"""
        
        # Add database in compact format
        for i, vaccine in enumerate(self.indici_database, 1):
            prompt += f"{i}. Code: {vaccine['vaccine_code']} | Name: {vaccine['vaccine_name']} | Brand: {vaccine['brand']} | Description: {vaccine['long_description']}\n"
        
        prompt += """
INSTRUCTIONS:
1. Search the ENTIRE database above for the best match
2. Consider medical equivalence, vaccine components, and clinical use
3. Match based on vaccine type, not just text similarity
4. Be consistent - same legacy vaccine should always map to same result
5. If no good medical match exists, return null

RESPONSE FORMAT (JSON only):
{
    "match_found": true/false,
    "vaccine_code": "exact code from database or null",
    "vaccine_name": "exact name from database or null", 
    "confidence": 0-100,
    "rationale": "medical explanation for the match"
}

CONFIDENCE CRITERIA:
- 95-100: Perfect medical match (same vaccine, different naming)
- 85-94: Very good match (same vaccine type/components)
- 75-84: Good match (similar vaccine family)
- 65-74: Moderate match (related vaccines)
- Below 65: No match (return null)

AUTO-ACCEPT THRESHOLD: 75% or higher

Be consistent and use medical knowledge. Respond with JSON only."""

        return prompt
    
    def get_llm_mapping(self, legacy_vaccine, retry_count=3):
        """Get mapping from LLM with retries."""
        
        for attempt in range(retry_count):
            try:
                prompt = self.create_llm_prompt(legacy_vaccine)
                
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a medical expert specializing in vaccine identification. Always respond with valid JSON only."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=500,
                    temperature=0.1
                )
                
                # Parse response
                response_text = response.choices[0].message.content.strip()
                
                # Clean JSON if needed
                if response_text.startswith('```json'):
                    response_text = response_text.replace('```json', '').replace('```', '').strip()
                
                result = json.loads(response_text)
                
                # Validate response
                if 'match_found' in result and 'confidence' in result:
                    return result
                else:
                    logger.warning(f"Invalid LLM response format: {result}")
                    
            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error (attempt {attempt + 1}): {e}")
                logger.warning(f"Raw response: {response_text}")
                
            except Exception as e:
                logger.warning(f"LLM request error (attempt {attempt + 1}): {e}")
                
            # Wait before retry
            if attempt < retry_count - 1:
                time.sleep(2)
        
        # Return no match if all attempts failed
        return {
            "match_found": False,
            "vaccine_code": None,
            "vaccine_name": None,
            "confidence": 0,
            "rationale": "LLM processing failed after retries"
        }
    
    def process_first_20_vaccines(self):
        """Process only the first 20 vaccines with pure LLM."""
        
        logger.info("Starting pure LLM mapping for first 20 vaccines...")
        
        # Get first 20 unmapped vaccines
        unmapped_mask = (
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
        )
        
        unmapped_vaccines = self.mapper.required_mapping_df[unmapped_mask].head(20)
        total_to_process = len(unmapped_vaccines)
        
        logger.info(f"Processing {total_to_process} vaccines with pure LLM...")
        
        # Counters
        auto_mapped = 0
        needs_review = 0
        no_match = 0
        failed = 0
        
        results = []
        
        for idx, (row_idx, row) in enumerate(unmapped_vaccines.iterrows(), 1):
            legacy_vaccine = {
                'code': row[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                'description': row[REQUIRED_MAPPING_COLUMNS['legacy_description']]
            }
            
            print(f"\nProcessing {idx}/{total_to_process}: {legacy_vaccine['code']} - {legacy_vaccine['description']}")
            
            # Get LLM mapping
            result = self.get_llm_mapping(legacy_vaccine)
            
            if result['match_found'] and result['confidence'] >= 75:
                # Auto-map with high confidence
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = result['vaccine_code']
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = result['vaccine_name']
                
                # Find full description
                full_vaccine = next((v for v in self.indici_database if v['vaccine_code'] == result['vaccine_code']), None)
                if full_vaccine:
                    self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = full_vaccine['long_description']
                
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = "llm_auto_mapped"
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"LLM auto-mapped (confidence: {result['confidence']}%) - {result['rationale']}"
                
                auto_mapped += 1
                print(f"✅ AUTO-MAPPED: {result['vaccine_name']} (confidence: {result['confidence']}%)")
                print(f"   Rationale: {result['rationale']}")
                
                results.append({
                    'legacy_code': legacy_vaccine['code'],
                    'legacy_desc': legacy_vaccine['description'],
                    'mapped_to': result['vaccine_name'],
                    'mapped_code': result['vaccine_code'],
                    'confidence': result['confidence'],
                    'rationale': result['rationale'],
                    'status': 'AUTO_MAPPED'
                })
                
            elif result['match_found'] and result['confidence'] >= 50:
                # Needs review
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NEEDS_REVIEW
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"LLM suggestion (confidence: {result['confidence']}%) - {result['rationale']} - NEEDS REVIEW"
                
                needs_review += 1
                print(f"⚠️  NEEDS REVIEW: {result['vaccine_name']} (confidence: {result['confidence']}%)")
                print(f"   Rationale: {result['rationale']}")
                
                results.append({
                    'legacy_code': legacy_vaccine['code'],
                    'legacy_desc': legacy_vaccine['description'],
                    'mapped_to': result['vaccine_name'],
                    'mapped_code': result['vaccine_code'],
                    'confidence': result['confidence'],
                    'rationale': result['rationale'],
                    'status': 'NEEDS_REVIEW'
                })
                
            else:
                # No match found
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"LLM found no suitable match - {result['rationale']}"
                
                no_match += 1
                print(f"❌ NO MATCH: {result['rationale']}")
                
                results.append({
                    'legacy_code': legacy_vaccine['code'],
                    'legacy_desc': legacy_vaccine['description'],
                    'mapped_to': 'NO MATCH',
                    'mapped_code': 'N/A',
                    'confidence': result['confidence'],
                    'rationale': result['rationale'],
                    'status': 'NO_MATCH'
                })
            
            # Small delay to avoid rate limits
            time.sleep(1)
        
        print(f"\n" + "="*60)
        print(f"PURE LLM MAPPING COMPLETED FOR FIRST 20 VACCINES")
        print(f"="*60)
        print(f"✅ Auto-mapped: {auto_mapped}")
        print(f"⚠️  Needs review: {needs_review}")
        print(f"❌ No match: {no_match}")
        print(f"❌ Failed: {failed}")
        
        return results
    
    def save_results(self):
        """Save results to Excel."""
        logger.info("Saving results to Excel...")
        return self.mapper.save_results()

def main():
    """Main function."""
    
    print("🤖 PURE LLM MAPPING - FIRST 20 VACCINES ONLY")
    print("=" * 60)
    print("This will map the first 20 vaccines using pure LLM approach")
    print("No fuzzy logic, no rule-based interference - just AI intelligence")
    print()
    
    try:
        # Initialize mapper
        mapper = PureLLMMapper()
        
        # Load data
        mapper.load_data()
        
        # Process first 20 vaccines
        results = mapper.process_first_20_vaccines()
        
        # Save results
        if mapper.save_results():
            print("\n✅ Results saved successfully!")
        else:
            print("\n❌ Failed to save results")
            return
        
        # Show detailed results
        print(f"\n📋 DETAILED RESULTS:")
        print("-" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['legacy_code']} - {result['legacy_desc'][:40]}...")
            print(f"   → {result['mapped_to']} ({result['mapped_code']})")
            print(f"   Status: {result['status']} | Confidence: {result['confidence']}%")
            print(f"   Rationale: {result['rationale']}")
            print()
        
        print(f"📁 Results saved to: {EXCEL_FILE}")
        print("📋 Check the 'Required Mapping' sheet for all mappings")
        
    except Exception as e:
        logger.error(f"Pure LLM mapping failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
