"""
Streamlit-based MCP (Human-in-the-Loop) Review UI for vaccine mapping.

This interface allows reviewers to:
1. Review LLM suggestions and ambiguous mappings
2. Accept, reject, or override mapping suggestions
3. Manually map vaccines to the standard list
4. Track progress and save decisions
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from vaccine_mapper import VaccineMapper
from config import *
from utils import *

# Configure Streamlit page
st.set_page_config(
    page_title=STREAMLIT_PAGE_TITLE,
    page_icon=STREAMLIT_PAGE_ICON,
    layout=STREAMLIT_LAYOUT
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@st.cache_data
def load_mapping_data():
    """Load mapping data with caching."""
    mapper = VaccineMapper(EXCEL_FILE)
    if mapper.load_data():
        mapper.initialize_required_mapping()
        return mapper
    return None

@st.cache_data
def get_indici_options(mapper):
    """Get Indici vaccine options for dropdown."""
    if mapper and not mapper.indici_df.empty:
        options = []
        for _, row in mapper.indici_df.iterrows():
            code = row[INDICI_COLUMNS['vaccine_code']]
            name = row[INDICI_COLUMNS['vaccine_name']]
            brand = row[INDICI_COLUMNS['brand']]
            
            display_text = f"{code} - {name}"
            if pd.notna(brand) and brand.strip():
                display_text += f" ({brand})"
            
            options.append({
                'display': display_text,
                'code': code,
                'name': name,
                'brand': brand,
                'description': row[INDICI_COLUMNS['long_description']]
            })
        
        return sorted(options, key=lambda x: x['display'])
    return []

def save_mapping_decision(mapper, row_idx, decision_type, indici_code=None, 
                         indici_name=None, indici_description=None, comments=None):
    """Save a mapping decision and update the Excel file."""
    
    try:
        # Update the mapping dataframe
        if decision_type == "accept":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_MANUAL_CONFIRMED
        elif decision_type == "reject":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = ""
        elif decision_type == "override":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_MANUAL_OVERRIDE
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = indici_code or ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = indici_name or ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = indici_description or ""
        
        # Update comments
        if comments:
            existing_comments = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']]
            if pd.notna(existing_comments) and existing_comments.strip():
                new_comments = f"{existing_comments} | Manual review: {comments}"
            else:
                new_comments = f"Manual review: {comments}"
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = new_comments
        
        # Save to Excel
        success = mapper.save_results()
        
        if success and decision_type in ["accept", "override"]:
            # Save to overrides file
            legacy_code = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['legacy_code']]
            legacy_desc = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['legacy_description']]
            legacy_norm = normalize_text(f"{legacy_code} {legacy_desc}")
            
            current_code = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']]
            current_name = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']]
            current_norm = normalize_text(f"{current_code} {current_name}")
            
            save_override(
                legacy_norm=legacy_norm,
                current_norm=current_norm,
                match_type=STATUS_MANUAL_CONFIRMED if decision_type == "accept" else STATUS_MANUAL_OVERRIDE,
                reason=comments or f"Manual {decision_type}",
                confirmed_by="MCP_Reviewer",
                file_path=OVERRIDES_FILE
            )
        
        return success
        
    except Exception as e:
        logger.error(f"Error saving mapping decision: {str(e)}")
        return False

def main():
    """Main Streamlit application."""
    
    st.title("💉 Vaccine Mapping Review (MCP)")
    st.markdown("Human-in-the-loop confirmation for vaccine mappings")
    
    # Load data
    mapper = load_mapping_data()
    
    if not mapper:
        st.error("❌ Failed to load mapping data. Please ensure vaccines.xlsx exists and run the mapping pipeline first.")
        st.stop()
    
    # Get items needing review
    review_statuses = [STATUS_NEEDS_REVIEW, STATUS_LLM_SUGGESTED, '']
    review_mask = mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isin(review_statuses) | \
                  mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna()
    
    review_items = mapper.required_mapping_df[review_mask].copy()
    
    if review_items.empty:
        st.success("🎉 All vaccines have been reviewed! No items need attention.")
        
        # Show summary
        st.subheader("📊 Mapping Summary")
        summary = mapper.get_mapping_summary()
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Vaccines", len(mapper.required_mapping_df))
            st.metric("Exact Matches", summary.get(STATUS_EXACT, 0))
        
        with col2:
            st.metric("Fuzzy Matches", summary.get(STATUS_FUZZY_HIGH, 0))
            st.metric("LLM Suggested", summary.get(STATUS_LLM_SUGGESTED, 0))
        
        with col3:
            st.metric("Manual Confirmed", summary.get(STATUS_MANUAL_CONFIRMED, 0))
            st.metric("Manual Override", summary.get(STATUS_MANUAL_OVERRIDE, 0))
        
        st.stop()
    
    # Progress indicator
    total_items = len(review_items)
    st.subheader(f"📋 Review Progress: {total_items} items remaining")
    
    # Item selector
    if total_items > 1:
        item_index = st.selectbox(
            "Select item to review:",
            range(total_items),
            format_func=lambda x: f"Item {x+1}: {review_items.iloc[x][REQUIRED_MAPPING_COLUMNS['legacy_description']][:50]}..."
        )
    else:
        item_index = 0
    
    current_item = review_items.iloc[item_index]
    current_row_idx = review_items.index[item_index]
    
    # Display current item
    st.markdown("---")
    st.subheader(f"🔍 Reviewing Item {item_index + 1} of {total_items}")
    
    # Legacy vaccine information
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📋 Legacy Vaccine")
        st.write(f"**Code:** {current_item[REQUIRED_MAPPING_COLUMNS['legacy_code']]}")
        st.write(f"**Description:** {current_item[REQUIRED_MAPPING_COLUMNS['legacy_description']]}")
        
        # Current status and comments
        current_status = current_item[REQUIRED_MAPPING_COLUMNS['mapping_status']]
        if pd.notna(current_status) and current_status:
            st.write(f"**Status:** {current_status}")
        
        current_comments = current_item[REQUIRED_MAPPING_COLUMNS['comments']]
        if pd.notna(current_comments) and current_comments:
            st.write(f"**Comments:** {current_comments}")
    
    with col2:
        st.markdown("### 🎯 Current Suggestion")
        
        current_indici_code = current_item[REQUIRED_MAPPING_COLUMNS['indici_code']]
        current_indici_name = current_item[REQUIRED_MAPPING_COLUMNS['indici_name']]
        current_indici_desc = current_item[REQUIRED_MAPPING_COLUMNS['indici_description']]
        
        if pd.notna(current_indici_code) and current_indici_code:
            st.write(f"**Code:** {current_indici_code}")
            st.write(f"**Name:** {current_indici_name}")
            st.write(f"**Description:** {current_indici_desc}")
        else:
            st.write("*No current suggestion*")
    
    # Decision interface
    st.markdown("---")
    st.subheader("✅ Make Decision")
    
    decision_type = st.radio(
        "Choose action:",
        ["accept", "reject", "override"],
        format_func=lambda x: {
            "accept": "✅ Accept current suggestion",
            "reject": "❌ Reject - no suitable match",
            "override": "🔧 Override with custom mapping"
        }[x]
    )
    
    # Override interface
    selected_vaccine = None
    if decision_type == "override":
        st.markdown("#### 🔧 Select Custom Mapping")
        
        indici_options = get_indici_options(mapper)
        
        if indici_options:
            # Search functionality
            search_term = st.text_input("🔍 Search vaccines:", placeholder="Type to search...")
            
            if search_term:
                filtered_options = [opt for opt in indici_options 
                                  if search_term.lower() in opt['display'].lower()]
            else:
                filtered_options = indici_options
            
            if filtered_options:
                selected_idx = st.selectbox(
                    "Select vaccine:",
                    range(len(filtered_options)),
                    format_func=lambda x: filtered_options[x]['display']
                )
                selected_vaccine = filtered_options[selected_idx]
                
                # Show selected vaccine details
                st.write(f"**Selected:** {selected_vaccine['display']}")
                st.write(f"**Description:** {selected_vaccine['description']}")
            else:
                st.warning("No vaccines found matching search term")
        else:
            st.error("No Indici vaccines available")
    
    # Comments
    review_comments = st.text_area(
        "💬 Review comments (optional):",
        placeholder="Add any notes about this decision..."
    )
    
    # Action buttons
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("💾 Save Decision", type="primary"):
            # Validate decision
            if decision_type == "override" and not selected_vaccine:
                st.error("Please select a vaccine for override")
            else:
                # Save the decision
                success = save_mapping_decision(
                    mapper=mapper,
                    row_idx=current_row_idx,
                    decision_type=decision_type,
                    indici_code=selected_vaccine['code'] if selected_vaccine else None,
                    indici_name=selected_vaccine['name'] if selected_vaccine else None,
                    indici_description=selected_vaccine['description'] if selected_vaccine else None,
                    comments=review_comments
                )
                
                if success:
                    st.success(f"✅ Decision saved successfully!")
                    st.balloons()
                    
                    # Clear cache and rerun to refresh data
                    st.cache_data.clear()
                    st.rerun()
                else:
                    st.error("❌ Failed to save decision")
    
    with col2:
        if st.button("⏭️ Skip Item"):
            if item_index < total_items - 1:
                st.rerun()
            else:
                st.info("This is the last item")
    
    with col3:
        if st.button("🔄 Refresh Data"):
            st.cache_data.clear()
            st.rerun()
    
    # Progress bar
    completed_items = len(mapper.required_mapping_df) - total_items
    total_all_items = len(mapper.required_mapping_df)
    progress = completed_items / total_all_items if total_all_items > 0 else 0
    
    st.progress(progress)
    st.caption(f"Overall progress: {completed_items}/{total_all_items} items completed ({progress*100:.1f}%)")

if __name__ == "__main__":
    main()
