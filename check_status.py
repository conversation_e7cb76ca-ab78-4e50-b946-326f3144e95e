import pandas as pd

# Read the current state
df = pd.read_excel('vaccines.xlsx', sheet_name='Required Mapping')

print('Current Mapping Status counts:')
print(df['Mapping Status'].value_counts(dropna=False))
print()

# Check unmapped
unmapped = df[df['Mapping Status'].isna() | (df['Mapping Status'] == '')]
print(f'Unmapped count: {len(unmapped)}')

if len(unmapped) > 0:
    print('\nSample unmapped rows:')
    print(unmapped[['Medtech Vaccine Code', 'Medtech Vaccine Description', 'Mapping Status']].head())
else:
    print('\nAll vaccines appear to be mapped!')

print(f'\nTotal rows: {len(df)}')
print(f'Mapped rows: {len(df) - len(unmapped)}')
print(f'Unmapped rows: {len(unmapped)}')
