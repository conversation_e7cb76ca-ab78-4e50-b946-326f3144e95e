"""
Core vaccine mapping module with cleaning, deduplication, and matching logic.

Workflow:
1. Load and clean data from Excel sheets
2. Apply override mappings
3. Exact matching pass
4. Fuzzy matching with high-confidence auto-mapping
5. LLM-assisted suggestions for ambiguous cases
6. Update Required Mapping sheet with results
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from rapidfuzz import fuzz, process

from config import *
from utils import *

logger = logging.getLogger(__name__)

class VaccineMapper:
    """Main class for vaccine mapping operations."""
    
    def __init__(self, excel_file: str = EXCEL_FILE):
        """
        Initialize the vaccine mapper.
        
        Args:
            excel_file: Path to the Excel workbook
        """
        self.excel_file = excel_file
        self.required_mapping_df = pd.DataFrame()
        self.indici_df = pd.DataFrame()
        self.legacy_df = pd.DataFrame()
        self.overrides_df = pd.DataFrame()
        
    def load_data(self) -> bool:
        """
        Load data from all Excel sheets.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load Required Mapping sheet (may be empty initially)
            self.required_mapping_df = safe_excel_read(self.excel_file, REQUIRED_MAPPING_SHEET)
            
            # Load Indici vaccines (target canonical list)
            self.indici_df = safe_excel_read(self.excel_file, INDICI_VACCINES_SHEET)
            if self.indici_df.empty:
                logger.error("Indici vaccines sheet is empty or not found")
                return False
                
            # Load Legacy vaccines (source to be mapped)
            self.legacy_df = safe_excel_read(self.excel_file, LEGACY_VACCINES_SHEET)
            if self.legacy_df.empty:
                logger.error("Legacy vaccines sheet is empty or not found")
                return False
                
            # Load overrides
            self.overrides_df = load_overrides(OVERRIDES_FILE)
            
            logger.info(f"Loaded {len(self.indici_df)} Indici vaccines, {len(self.legacy_df)} legacy vaccines")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False
    
    def clean_and_normalize_data(self):
        """Clean and normalize all vaccine data."""
        
        # Clean Indici data
        for col in INDICI_COLUMNS.values():
            if col in self.indici_df.columns:
                self.indici_df[f'{col}_normalized'] = self.indici_df[col].apply(normalize_text)
        
        # Clean Legacy data  
        for col in LEGACY_COLUMNS.values():
            if col in self.legacy_df.columns:
                self.legacy_df[f'{col}_normalized'] = self.legacy_df[col].apply(normalize_text)
        
        # Remove exact duplicates within each dataset but preserve dose variants
        initial_indici_count = len(self.indici_df)
        initial_legacy_count = len(self.legacy_df)
        
        # For Indici: deduplicate on normalized vaccine code + name
        indici_dedup_cols = [f"{INDICI_COLUMNS['vaccine_code']}_normalized", 
                            f"{INDICI_COLUMNS['vaccine_name']}_normalized"]
        self.indici_df = self.indici_df.drop_duplicates(subset=indici_dedup_cols, keep='first')
        
        # For Legacy: deduplicate on normalized code + description
        legacy_dedup_cols = [f"{LEGACY_COLUMNS['vaccine_code']}_normalized",
                            f"{LEGACY_COLUMNS['vaccine_description']}_normalized"]
        self.legacy_df = self.legacy_df.drop_duplicates(subset=legacy_dedup_cols, keep='first')
        
        logger.info(f"Deduplication: Indici {initial_indici_count} -> {len(self.indici_df)}, "
                   f"Legacy {initial_legacy_count} -> {len(self.legacy_df)}")
    
    def initialize_required_mapping(self):
        """Initialize Required Mapping sheet with legacy vaccine data if empty."""
        
        if self.required_mapping_df.empty or len(self.required_mapping_df) == 0:
            logger.info("Initializing Required Mapping sheet with legacy vaccine data")
            
            # Create initial mapping structure from legacy data
            mapping_data = []
            for _, row in self.legacy_df.iterrows():
                mapping_row = {
                    REQUIRED_MAPPING_COLUMNS['legacy_code']: row.get(LEGACY_COLUMNS['vaccine_code'], ''),
                    REQUIRED_MAPPING_COLUMNS['legacy_description']: row.get(LEGACY_COLUMNS['vaccine_description'], ''),
                    REQUIRED_MAPPING_COLUMNS['indici_code']: '',
                    REQUIRED_MAPPING_COLUMNS['indici_name']: '',
                    REQUIRED_MAPPING_COLUMNS['indici_description']: '',
                    REQUIRED_MAPPING_COLUMNS['mapping_status']: '',
                    REQUIRED_MAPPING_COLUMNS['comments']: ''
                }
                mapping_data.append(mapping_row)
            
            self.required_mapping_df = pd.DataFrame(mapping_data)
        
        # Add normalized columns for matching
        self.required_mapping_df['legacy_code_normalized'] = self.required_mapping_df[
            REQUIRED_MAPPING_COLUMNS['legacy_code']].apply(normalize_text)
        self.required_mapping_df['legacy_description_normalized'] = self.required_mapping_df[
            REQUIRED_MAPPING_COLUMNS['legacy_description']].apply(normalize_text)
    
    def apply_overrides(self):
        """Apply manual override mappings."""
        
        if self.overrides_df.empty:
            return
        
        applied_count = 0
        for _, override in self.overrides_df.iterrows():
            # Find matching legacy records
            mask = (self.required_mapping_df['legacy_code_normalized'] == override['legacy_norm']) | \
                   (self.required_mapping_df['legacy_description_normalized'] == override['legacy_norm'])
            
            if mask.any():
                # Find corresponding Indici record
                indici_mask = (self.indici_df[f"{INDICI_COLUMNS['vaccine_code']}_normalized"] == override['current_norm']) | \
                             (self.indici_df[f"{INDICI_COLUMNS['vaccine_name']}_normalized"] == override['current_norm'])
                
                if indici_mask.any():
                    indici_row = self.indici_df[indici_mask].iloc[0]
                    
                    # Apply override
                    self.required_mapping_df.loc[mask, REQUIRED_MAPPING_COLUMNS['indici_code']] = \
                        indici_row[INDICI_COLUMNS['vaccine_code']]
                    self.required_mapping_df.loc[mask, REQUIRED_MAPPING_COLUMNS['indici_name']] = \
                        indici_row[INDICI_COLUMNS['vaccine_name']]
                    self.required_mapping_df.loc[mask, REQUIRED_MAPPING_COLUMNS['indici_description']] = \
                        indici_row[INDICI_COLUMNS['long_description']]
                    self.required_mapping_df.loc[mask, REQUIRED_MAPPING_COLUMNS['mapping_status']] = \
                        override['match_type']
                    self.required_mapping_df.loc[mask, REQUIRED_MAPPING_COLUMNS['comments']] = \
                        f"Override applied: {override['reason']}"
                    
                    applied_count += 1
        
        logger.info(f"Applied {applied_count} override mappings")
    
    def exact_matching_pass(self):
        """Perform exact matching on codes and normalized text."""
        
        exact_matches = 0
        
        # Get unmapped rows
        unmapped_mask = (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') | \
                       (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
        
        for idx, row in self.required_mapping_df[unmapped_mask].iterrows():
            legacy_code_norm = row['legacy_code_normalized']
            legacy_desc_norm = row['legacy_description_normalized']
            
            # Try exact match on vaccine code
            indici_match = None
            match_field = None
            
            # Match on vaccine code
            code_mask = self.indici_df[f"{INDICI_COLUMNS['vaccine_code']}_normalized"] == legacy_code_norm
            if code_mask.any():
                indici_match = self.indici_df[code_mask].iloc[0]
                match_field = "VaccineCode"
            
            # Match on vaccine name
            if indici_match is None:
                name_mask = self.indici_df[f"{INDICI_COLUMNS['vaccine_name']}_normalized"] == legacy_desc_norm
                if name_mask.any():
                    indici_match = self.indici_df[name_mask].iloc[0]
                    match_field = "VaccineName"
            
            # Match on brand
            if indici_match is None:
                brand_mask = self.indici_df[f"{INDICI_COLUMNS['brand']}_normalized"] == legacy_desc_norm
                if brand_mask.any():
                    indici_match = self.indici_df[brand_mask].iloc[0]
                    match_field = "Brand"
            
            # Match on long description
            if indici_match is None:
                desc_mask = self.indici_df[f"{INDICI_COLUMNS['long_description']}_normalized"] == legacy_desc_norm
                if desc_mask.any():
                    indici_match = self.indici_df[desc_mask].iloc[0]
                    match_field = "LongDescription"
            
            # Apply exact match if found
            if indici_match is not None:
                self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = \
                    str(indici_match[INDICI_COLUMNS['vaccine_code']])
                self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = \
                    str(indici_match[INDICI_COLUMNS['vaccine_name']])
                self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = \
                    str(indici_match[INDICI_COLUMNS['long_description']])
                self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_EXACT
                self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                    f"Exact match on {match_field}"
                
                exact_matches += 1
        
        logger.info(f"Found {exact_matches} exact matches")
    
    def fuzzy_matching_pass(self):
        """Perform fuzzy matching with high-confidence auto-mapping."""
        
        fuzzy_matches = 0
        
        # Get unmapped rows
        unmapped_mask = (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') | \
                       (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
        
        for idx, row in self.required_mapping_df[unmapped_mask].iterrows():
            legacy_desc_norm = row['legacy_description_normalized']
            
            if not legacy_desc_norm:
                continue
            
            # Create search corpus from Indici vaccines
            search_corpus = []
            indici_indices = []
            
            for i, indici_row in self.indici_df.iterrows():
                # Add all searchable fields
                fields = [
                    indici_row[f"{INDICI_COLUMNS['vaccine_name']}_normalized"],
                    indici_row[f"{INDICI_COLUMNS['brand']}_normalized"],
                    indici_row[f"{INDICI_COLUMNS['long_description']}_normalized"]
                ]
                
                for field in fields:
                    if field:
                        search_corpus.append(field)
                        indici_indices.append(i)
            
            # Find best fuzzy matches
            if search_corpus:
                matches = process.extract(legacy_desc_norm, search_corpus, limit=3, scorer=fuzz.token_set_ratio)
                
                if matches and matches[0][1] >= FUZZY_HIGH_THRESHOLD:
                    # High confidence fuzzy match - auto-apply
                    best_match_idx = indici_indices[search_corpus.index(matches[0][0])]
                    indici_match = self.indici_df.iloc[best_match_idx]

                    self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = \
                        str(indici_match[INDICI_COLUMNS['vaccine_code']])
                    self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = \
                        str(indici_match[INDICI_COLUMNS['vaccine_name']])
                    self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = \
                        str(indici_match[INDICI_COLUMNS['long_description']])
                    self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_FUZZY_HIGH
                    self.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                        f"High confidence fuzzy match (score: {matches[0][1]:.1f})"
                    
                    fuzzy_matches += 1
        
        logger.info(f"Found {fuzzy_matches} high-confidence fuzzy matches")
    
    def get_candidates_for_llm(self, legacy_text: str, top_k: int = 3) -> List[Dict]:
        """
        Get top candidate matches for LLM evaluation.
        
        Args:
            legacy_text: Legacy vaccine description
            top_k: Number of top candidates to return
            
        Returns:
            List of candidate dictionaries
        """
        if not legacy_text:
            return []
        
        # Create search corpus
        candidates = []
        
        for _, indici_row in self.indici_df.iterrows():
            # Calculate scores against multiple fields
            name_score = calculate_match_score(legacy_text, 
                                             indici_row[f"{INDICI_COLUMNS['vaccine_name']}_normalized"])
            brand_score = calculate_match_score(legacy_text,
                                              indici_row[f"{INDICI_COLUMNS['brand']}_normalized"])
            desc_score = calculate_match_score(legacy_text,
                                             indici_row[f"{INDICI_COLUMNS['long_description']}_normalized"])
            
            max_score = max(name_score, brand_score, desc_score)
            
            if max_score >= FUZZY_PRESELECTION_THRESHOLD:
                candidate = {
                    'vaccine_code': indici_row[INDICI_COLUMNS['vaccine_code']],
                    'vaccine_name': indici_row[INDICI_COLUMNS['vaccine_name']],
                    'brand': indici_row[INDICI_COLUMNS['brand']],
                    'long_description': indici_row[INDICI_COLUMNS['long_description']],
                    'similarity_score': max_score
                }
                candidates.append(candidate)
        
        # Sort by similarity score and return top candidates
        candidates.sort(key=lambda x: x['similarity_score'], reverse=True)
        return candidates[:top_k]
    
    def save_results(self) -> bool:
        """
        Save mapping results back to Excel file.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove normalized columns before saving
            cols_to_save = [col for col in self.required_mapping_df.columns 
                           if not col.endswith('_normalized')]
            df_to_save = self.required_mapping_df[cols_to_save]
            
            # Save to Excel
            return safe_excel_write(df_to_save, self.excel_file, REQUIRED_MAPPING_SHEET)
            
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            return False
    
    def get_mapping_summary(self) -> Dict[str, int]:
        """
        Get summary of mapping results.
        
        Returns:
            Dictionary with counts by status
        """
        if self.required_mapping_df.empty:
            return {}
        
        status_counts = self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].value_counts().to_dict()
        
        # Add unmapped count
        unmapped = len(self.required_mapping_df[
            (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') |
            (self.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
        ])
        
        if unmapped > 0:
            status_counts['unmapped'] = unmapped
        
        return status_counts
