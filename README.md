# Vaccine Data Mapping System

A complete Python automation system for vaccine data mapping with human-in-the-loop confirmation (MCP). This system performs intelligent mapping between legacy vaccine systems and standardized vaccine lists using exact matching, fuzzy matching, and LLM-assisted suggestions.

## 🚀 Features

- **Automated Data Processing**: Clean, normalize, and deduplicate vaccine data
- **Multi-tier Matching**: Exact → Fuzzy → LLM-assisted matching pipeline
- **Human-in-the-Loop Review**: Streamlit-based UI for reviewing ambiguous mappings
- **Override Management**: Persistent storage of manual mapping decisions
- **Progress Tracking**: Comprehensive reporting and status tracking
- **Excel Integration**: Direct read/write to Excel workbooks

## 📋 Requirements

- Python 3.8+
- Excel file named `vaccines.xlsx` with specific sheet structure
- OpenAI API key (for LLM-assisted matching)

### Required Excel Sheets

1. **"Required Mapping"** - Primary sheet for storing mapping results
2. **"Indici Vaccines Standard list"** - Target canonical vaccine list with columns:
   - VaccineCode
   - VaccineName  
   - Brand
   - LongDescription
3. **"Legacy System Full Vaccine List"** - Source vaccines with columns:
   - Medtech Vaccine Code
   - Medtech Vaccine Description
   - WHENIMM

## 🛠️ Installation

### Option 1: Automated Setup

1. Clone or download this repository
2. Run the setup script:
   ```bash
   python setup.py
   ```
3. Follow the instructions to activate the virtual environment
4. Run setup again from within the virtual environment

### Option 2: Manual Setup

1. Create and activate virtual environment:
   ```bash
   python -m venv .venv
   
   # Windows
   .venv\Scripts\activate
   
   # Linux/Mac
   source .venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create environment file:
   ```bash
   cp .env.template .env
   ```

4. Edit `.env` and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_actual_api_key_here
   ```

## 🎯 Usage

### Quick Start

1. Place your `vaccines.xlsx` file in the project directory
2. Run the complete pipeline:
   ```bash
   python run_mapping.py
   ```
3. Review ambiguous mappings in the web UI that launches automatically

### Command Line Options

```bash
# Full pipeline with LLM and UI
python run_mapping.py

# Skip LLM processing
python run_mapping.py --no-llm

# Don't launch UI automatically  
python run_mapping.py --no-ui

# Basic matching only
python run_mapping.py --no-llm --no-ui
```

### Manual Review UI

Launch the review interface separately:
```bash
streamlit run mcp_server.py
```

## 🔄 Workflow

### 1. Data Loading & Cleaning
- Loads data from all Excel sheets
- Normalizes text (lowercase, trim, remove punctuation)
- Deduplicates exact matches while preserving dose variants

### 2. Override Application
- Applies previously confirmed manual mappings from `overrides.csv`
- Ensures consistent decisions across runs

### 3. Exact Matching
- Matches on exact normalized vaccine codes and names
- Checks against VaccineCode, VaccineName, Brand, LongDescription

### 4. Fuzzy Matching  
- Uses RapidFuzz for similarity scoring
- Auto-applies high-confidence matches (≥90% similarity)
- Considers token-based and partial matching

### 5. LLM-Assisted Suggestions
- Sends ambiguous cases to GPT-4 for semantic analysis
- Provides structured JSON responses with confidence scores
- Auto-applies high-confidence suggestions (≥85%)

### 6. Human Review
- Streamlit UI for reviewing remaining ambiguous cases
- Options to Accept, Reject, or Override suggestions
- Saves decisions to Excel and override files

## 📊 Output

### Required Mapping Sheet Columns
- **NIR / Indici Vaccine Code** - Mapped vaccine code
- **IndiciVaccineName** - Mapped vaccine name  
- **IndiciVaccDescription** - Mapped vaccine description
- **Mapping Status** - Status of mapping (exact, fuzzy_high, llm_suggested, etc.)
- **Comments** - Rationale and reviewer notes

### Mapping Status Values
- `exact` - Exact text match found
- `fuzzy_high` - High-confidence fuzzy match (≥90%)
- `llm_suggested` - LLM suggested with high confidence (≥85%)
- `needs_review` - Requires human review
- `manual_confirmed` - Manually confirmed by reviewer
- `manual_override` - Custom mapping by reviewer
- `no_match` - No suitable match found

### Override File
`overrides.csv` contains persistent manual mappings:
- legacy_norm - Normalized legacy vaccine text
- current_norm - Normalized target vaccine text  
- match_type - Type of manual decision
- reason - Explanation for mapping
- confirmed_by - Reviewer identifier
- confirmed_on - Timestamp

## 🔧 Configuration

Key settings in `config.py`:
- **FUZZY_HIGH_THRESHOLD** (90) - Auto-apply fuzzy matches above this score
- **LLM_CONFIDENCE_THRESHOLD** (85) - Auto-apply LLM suggestions above this score
- **OPENAI_MODEL** (gpt-4) - OpenAI model to use
- **OPENAI_TEMPERATURE** (0.1) - Temperature for LLM responses

## 📁 File Structure

```
vaccine_mapping/
├── vaccines.xlsx              # Input Excel workbook
├── requirements.txt           # Python dependencies
├── config.py                 # Configuration settings
├── utils.py                  # Utility functions
├── vaccine_mapper.py         # Core mapping logic
├── llm_assistant.py          # LLM integration
├── run_mapping.py            # Main CLI script
├── mcp_server.py             # Streamlit review UI
├── setup.py                  # Setup script
├── overrides.csv             # Manual mapping overrides
├── .env                      # Environment variables
└── README.md                 # This file
```

## 🐛 Troubleshooting

### Common Issues

1. **"No module named 'pandas'"**
   - Ensure virtual environment is activated
   - Run `pip install -r requirements.txt`

2. **"Excel file not found"**
   - Ensure `vaccines.xlsx` is in the project directory
   - Check file name spelling and case

3. **"LLM processing failed"**
   - Verify OpenAI API key in `.env` file
   - Check API key has sufficient credits
   - Ensure internet connection

4. **"Streamlit command not found"**
   - Ensure streamlit is installed: `pip install streamlit`
   - Try running with: `python -m streamlit run mcp_server.py`

### Logging

Check `vaccine_mapping.log` for detailed execution logs and error messages.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the log files for error details
3. Create an issue with detailed error information and steps to reproduce
