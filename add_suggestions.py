#!/usr/bin/env python3
"""
Add intelligent suggestions to vaccines marked as 'needs_review' without using LLM.
This provides similar functionality to LLM suggestions using advanced fuzzy matching.
"""

import pandas as pd
import numpy as np
from rapidfuzz import fuzz, process
from vaccine_mapper import VaccineMapper
from config import *
from utils import *

def get_smart_suggestions(legacy_text, indici_df, top_k=3):
    """
    Get smart suggestions using multiple fuzzy matching techniques.
    
    Args:
        legacy_text: Legacy vaccine description
        indici_df: Indici vaccines dataframe
        top_k: Number of suggestions to return
        
    Returns:
        List of suggestion dictionaries
    """
    if not legacy_text:
        return []
    
    suggestions = []
    
    # Create search corpus with multiple fields
    search_items = []
    for _, row in indici_df.iterrows():
        # Combine multiple fields for better matching
        fields = [
            row[INDICI_COLUMNS['vaccine_name']],
            row[INDICI_COLUMNS['brand']],
            row[INDICI_COLUMNS['long_description']]
        ]
        
        for field in fields:
            if pd.notna(field) and field.strip():
                search_items.append({
                    'text': normalize_text(field),
                    'original_field': field,
                    'row_data': row
                })
    
    if not search_items:
        return []
    
    # Use multiple fuzzy matching methods
    search_texts = [item['text'] for item in search_items]
    
    # Method 1: Token set ratio (best for medical terms)
    matches1 = process.extract(legacy_text, search_texts, 
                              scorer=fuzz.token_set_ratio, limit=top_k*2)
    
    # Method 2: Partial ratio (good for partial matches)
    matches2 = process.extract(legacy_text, search_texts, 
                              scorer=fuzz.partial_ratio, limit=top_k*2)
    
    # Method 3: Token sort ratio (good for reordered words)
    matches3 = process.extract(legacy_text, search_texts, 
                              scorer=fuzz.token_sort_ratio, limit=top_k*2)
    
    # Combine and score all matches
    all_matches = {}

    for matches, weight in [(matches1, 1.0), (matches2, 0.8), (matches3, 0.9)]:
        for match_text, score, _ in matches:  # rapidfuzz returns (text, score, index)
            if match_text in all_matches:
                # Weighted average of scores
                all_matches[match_text] = max(all_matches[match_text], score * weight)
            else:
                all_matches[match_text] = score * weight
    
    # Get top matches
    sorted_matches = sorted(all_matches.items(), key=lambda x: x[1], reverse=True)
    
    # Convert to suggestions
    seen_vaccines = set()
    for match_text, score in sorted_matches[:top_k*3]:  # Get more to filter duplicates
        # Find the original item
        item = next((item for item in search_items if item['text'] == match_text), None)
        if item:
            row_data = item['row_data']
            vaccine_code = row_data[INDICI_COLUMNS['vaccine_code']]
            
            # Avoid duplicate vaccines
            if vaccine_code not in seen_vaccines:
                seen_vaccines.add(vaccine_code)
                
                suggestion = {
                    'vaccine_code': vaccine_code,
                    'vaccine_name': row_data[INDICI_COLUMNS['vaccine_name']],
                    'brand': row_data[INDICI_COLUMNS['brand']],
                    'long_description': row_data[INDICI_COLUMNS['long_description']],
                    'similarity_score': score,
                    'matched_field': item['original_field'],
                    'confidence_level': get_confidence_level(score),
                    'rationale': generate_rationale(legacy_text, item['original_field'], score)
                }
                suggestions.append(suggestion)
                
                if len(suggestions) >= top_k:
                    break
    
    return suggestions

def get_confidence_level(score):
    """Get confidence level based on score."""
    if score >= 90:
        return "High"
    elif score >= 75:
        return "Medium"
    elif score >= 60:
        return "Low"
    else:
        return "Very Low"

def generate_rationale(legacy_text, matched_field, score):
    """Generate a rationale for the match."""
    confidence = get_confidence_level(score)
    
    rationale = f"{confidence} confidence match (score: {score:.1f}%). "
    
    if score >= 90:
        rationale += "Very strong similarity in vaccine terminology. "
    elif score >= 75:
        rationale += "Good similarity in vaccine names or descriptions. "
    elif score >= 60:
        rationale += "Moderate similarity, may be related vaccine. "
    else:
        rationale += "Low similarity, review carefully. "
    
    # Add specific matching info
    if "measles" in legacy_text.lower() and "measles" in matched_field.lower():
        rationale += "Both contain measles vaccine components. "
    elif "hepatitis" in legacy_text.lower() and "hepatitis" in matched_field.lower():
        rationale += "Both are hepatitis vaccines. "
    elif "influenza" in legacy_text.lower() and "influenza" in matched_field.lower():
        rationale += "Both are influenza vaccines. "
    elif "pneumo" in legacy_text.lower() and "pneumo" in matched_field.lower():
        rationale += "Both are pneumococcal vaccines. "
    
    rationale += f"Matched against: {matched_field[:50]}..."
    
    return rationale

def add_suggestions_to_excel():
    """Add suggestions to vaccines marked as needs_review."""
    
    print("🔍 Adding intelligent suggestions to vaccines needing review...")
    
    # Load data
    mapper = VaccineMapper(EXCEL_FILE)
    mapper.load_data()
    mapper.clean_and_normalize_data()
    
    # Get vaccines needing review
    needs_review_mask = mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == STATUS_NEEDS_REVIEW
    needs_review_count = needs_review_mask.sum()
    
    print(f"Found {needs_review_count} vaccines needing review")
    
    if needs_review_count == 0:
        print("✅ No vaccines need suggestions!")
        return
    
    suggestions_added = 0
    
    for idx, row in mapper.required_mapping_df[needs_review_mask].iterrows():
        legacy_desc = normalize_text(row[REQUIRED_MAPPING_COLUMNS['legacy_description']])
        legacy_code = row[REQUIRED_MAPPING_COLUMNS['legacy_code']]
        
        print(f"Processing: {legacy_code} - {legacy_desc[:50]}...")
        
        # Get suggestions
        suggestions = get_smart_suggestions(legacy_desc, mapper.indici_df, top_k=3)
        
        if suggestions:
            # Format suggestions for comments
            comment_parts = []
            
            # Best suggestion
            best = suggestions[0]
            if best['similarity_score'] >= 85:
                # High confidence - suggest mapping
                mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = best['vaccine_code']
                mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = best['vaccine_name']
                mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = best['long_description']
                mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = "ai_suggested"
                
                comment_parts.append(f"AI SUGGESTED: {best['vaccine_name']} ({best['vaccine_code']}) - {best['rationale']}")
            else:
                comment_parts.append(f"TOP SUGGESTION: {best['vaccine_name']} ({best['vaccine_code']}) - {best['rationale']}")
            
            # Add alternatives
            if len(suggestions) > 1:
                alternatives = []
                for alt in suggestions[1:]:
                    alternatives.append(f"{alt['vaccine_name']} ({alt['similarity_score']:.1f}%)")
                comment_parts.append(f"ALTERNATIVES: {'; '.join(alternatives)}")
            
            # Update comments
            new_comment = " | ".join(comment_parts)
            mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = new_comment
            
            suggestions_added += 1
        else:
            # No suggestions found
            mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = "No suitable matches found in standard vaccine list"
    
    # Save results
    print(f"\n💾 Saving {suggestions_added} suggestions to Excel...")
    success = mapper.save_results()
    
    if success:
        print("✅ Suggestions added successfully!")
        
        # Print summary
        final_summary = mapper.get_mapping_summary()
        print("\n📊 Updated Summary:")
        for status, count in final_summary.items():
            print(f"   {status}: {count}")
        
        print(f"\n🎯 Result: Added intelligent suggestions to {suggestions_added} vaccines")
        print("💡 You can now review these in the web interface or Excel file")
    else:
        print("❌ Failed to save suggestions")

def main():
    """Main function."""
    print("🤖 AI-Style Vaccine Mapping Suggestions")
    print("=" * 50)
    print("This will add intelligent suggestions to vaccines marked as 'needs_review'")
    print("using advanced fuzzy matching techniques (no API required).")
    print()
    
    add_suggestions_to_excel()

if __name__ == "__main__":
    main()
