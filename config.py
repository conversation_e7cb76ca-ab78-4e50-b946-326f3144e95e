"""
Configuration settings for the vaccine mapping system.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# File paths
EXCEL_FILE = "vaccines.xlsx"
OVERRIDES_FILE = "overrides.csv"

# Excel sheet names
REQUIRED_MAPPING_SHEET = "Required Mapping"
INDICI_VACCINES_SHEET = "Indici Vaccines Standard list"
LEGACY_VACCINES_SHEET = "Legacy System Full Vaccine List"

# Column mappings for Required Mapping sheet
REQUIRED_MAPPING_COLUMNS = {
    'legacy_code': 'Medtech Vaccine Code',
    'legacy_description': 'Medtech Vaccine Description',
    'indici_code': 'NIR / Indici Vaccine Code',
    'indici_name': 'IndiciVaccineName',
    'indici_description': 'IndiciVaccDescription',
    'mapping_status': 'Mapping Status',
    'comments': 'Comments'
}

# Column mappings for Indici Vaccines sheet
INDICI_COLUMNS = {
    'vaccine_code': 'VaccineCode',
    'vaccine_name': 'VaccineName',
    'brand': 'Brand',
    'long_description': 'LongDescription'
}

# Column mappings for Legacy Vaccines sheet
LEGACY_COLUMNS = {
    'vaccine_code': 'Medtech Vaccine Code',
    'vaccine_description': 'Medtech Vaccine Description',
    'whenimm': 'WHENIMM'
}

# Matching thresholds
FUZZY_HIGH_THRESHOLD = 90
LLM_CONFIDENCE_THRESHOLD = 85
FUZZY_PRESELECTION_THRESHOLD = 70

# Mapping status values
STATUS_EXACT = "exact"
STATUS_FUZZY_HIGH = "fuzzy_high"
STATUS_LLM_SUGGESTED = "llm_suggested"
STATUS_NEEDS_REVIEW = "needs_review"
STATUS_MANUAL_CONFIRMED = "manual_confirmed"
STATUS_MANUAL_OVERRIDE = "manual_override"
STATUS_NO_MATCH = "no_match"

# OpenAI settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = "gpt-4"
OPENAI_MAX_TOKENS = 1000
OPENAI_TEMPERATURE = 0.1

# Streamlit settings
STREAMLIT_PAGE_TITLE = "Vaccine Mapping Review (MCP)"
STREAMLIT_PAGE_ICON = "💉"
STREAMLIT_LAYOUT = "wide"
