#!/usr/bin/env python3
"""
Reset fuzzy mappings and run pure LLM-only approach for consistency.
This addresses the issue where fuzzy logic creates inconsistent mappings.
"""

import pandas as pd
import logging
from vaccine_mapper import VaccineMapper
from llm_assistant import LLMVaccineAssistant
from config import *

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_fuzzy_mappings():
    """Reset all fuzzy mappings to allow LLM-only processing."""
    
    print("🔄 Resetting fuzzy mappings...")
    
    # Load current mapping data
    df = pd.read_excel(EXCEL_FILE, sheet_name=REQUIRED_MAPPING_SHEET)
    
    # Count current mappings
    fuzzy_mask = df['Mapping Status'] == STATUS_FUZZY_HIGH
    fuzzy_count = fuzzy_mask.sum()
    
    print(f"Found {fuzzy_count} fuzzy mappings to reset")
    
    if fuzzy_count > 0:
        # Reset fuzzy mappings
        df.loc[fuzzy_mask, 'NIR / Indici Vaccine Code'] = ''
        df.loc[fuzzy_mask, 'IndiciVaccineName'] = ''
        df.loc[fuzzy_mask, 'IndiciVaccDescription'] = ''
        df.loc[fuzzy_mask, 'Mapping Status'] = ''
        df.loc[fuzzy_mask, 'Comments'] = 'Reset for LLM-only processing'
        
        # Save back to Excel
        with pd.ExcelWriter(EXCEL_FILE, mode='a', if_sheet_exists='replace', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=REQUIRED_MAPPING_SHEET, index=False)
        
        print(f"✅ Reset {fuzzy_count} fuzzy mappings")
    else:
        print("✅ No fuzzy mappings to reset")
    
    return fuzzy_count

def run_llm_only_mapping():
    """Run mapping with LLM-only approach."""
    
    print("\n🤖 Running LLM-only mapping approach...")
    
    # Initialize mapper
    mapper = VaccineMapper(EXCEL_FILE)
    
    # Load and clean data
    if not mapper.load_data():
        print("❌ Failed to load data")
        return False
    
    mapper.clean_and_normalize_data()
    mapper.initialize_required_mapping()
    
    # Apply overrides
    mapper.apply_overrides()
    
    # Exact matching only
    mapper.exact_matching_pass()
    
    # Skip fuzzy matching completely
    print("⏭️  Skipping fuzzy matching for consistency")
    
    # LLM processing
    print("🤖 Processing with LLM (full database approach)...")
    llm_assistant = LLMVaccineAssistant()
    
    if llm_assistant.client:
        processed = llm_assistant.batch_process_unmapped(mapper)
        print(f"✅ LLM processed {processed} vaccines")
    else:
        print("❌ LLM not available - check API key")
        return False
    
    # Save results
    if mapper.save_results():
        print("✅ Results saved successfully")
        
        # Print summary
        summary = mapper.get_mapping_summary()
        print("\n📊 Final Summary:")
        total = len(mapper.required_mapping_df)
        
        for status, count in summary.items():
            percentage = (count / total * 100) if total > 0 else 0
            status_display = {
                STATUS_EXACT: "✅ Exact matches",
                STATUS_LLM_SUGGESTED: "🤖 LLM auto-mapped",
                STATUS_NEEDS_REVIEW: "⚠️  Needs human review",
                STATUS_MANUAL_CONFIRMED: "👤 Manually confirmed",
                STATUS_MANUAL_OVERRIDE: "🔧 Manual overrides",
                STATUS_NO_MATCH: "❌ No matches found",
                'unmapped': "❓ Unmapped"
            }.get(status, f"📊 {status}")
            
            print(f"{status_display}: {count} ({percentage:.1f}%)")
        
        return True
    else:
        print("❌ Failed to save results")
        return False

def main():
    """Main function."""
    
    print("🔧 FIXING FUZZY LOGIC INCONSISTENCY")
    print("=" * 50)
    print("This will:")
    print("1. Reset all fuzzy mappings that cause inconsistencies")
    print("2. Run pure LLM-only approach for consistent results")
    print("3. Provide human review interface for remaining items")
    print()
    
    # Step 1: Reset fuzzy mappings
    reset_count = reset_fuzzy_mappings()
    
    # Step 2: Run LLM-only mapping
    success = run_llm_only_mapping()
    
    if success:
        print("\n🎉 LLM-only mapping completed successfully!")
        print("\n💡 Benefits of this approach:")
        print("   ✅ Consistent mappings (same legacy vaccine → same target)")
        print("   ✅ AI-powered semantic understanding")
        print("   ✅ No fuzzy logic inconsistencies")
        print("   ✅ Human review for ambiguous cases")
        
        print(f"\n🚀 Next step: Review remaining items in MCP interface")
        print("   streamlit run mcp_server.py")
    else:
        print("\n❌ LLM mapping failed - check API key and try again")

if __name__ == "__main__":
    main()
