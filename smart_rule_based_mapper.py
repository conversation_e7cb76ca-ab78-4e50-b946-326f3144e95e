#!/usr/bin/env python3
"""
Smart Rule-Based Vaccine Mapper - NO API REQUIRED
This system uses intelligent rules to map vaccines automatically without LLM API calls.
Addresses fuzzy logic inconsistency while providing full automation.
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime
import logging
from rapidfuzz import fuzz

from vaccine_mapper import VaccineMapper
from config import *
from utils import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('smart_mapping.log')
    ]
)
logger = logging.getLogger(__name__)

class SmartRuleBasedMapper:
    """Smart rule-based vaccine mapper with medical knowledge."""
    
    def __init__(self):
        """Initialize the smart mapper."""
        self.mapper = None
        self.vaccine_rules = self._create_vaccine_rules()
        
    def _create_vaccine_rules(self):
        """Create intelligent vaccine mapping rules."""
        return {
            # Exact medical equivalents
            'exact_medical_matches': {
                'mmr': ['measles', 'mumps', 'rubella'],
                'dtp': ['diphtheria', 'tetanus', 'pertussis'],
                'dtap': ['diphtheria', 'tetanus', 'pertussis', 'acellular'],
                'tdap': ['tetanus', 'diphtheria', 'pertussis'],
                'td': ['tetanus', 'diphtheria'],
                'dt': ['diphtheria', 'tetanus'],
                'hep': ['hepatitis'],
                'hepb': ['hepatitis', 'b'],
                'hepa': ['hepatitis', 'a'],
                'hib': ['haemophilus', 'influenzae'],
                'ipv': ['polio', 'inactivated'],
                'opv': ['polio', 'oral'],
                'pcv': ['pneumococcal', 'conjugate'],
                'ppsv': ['pneumococcal', 'polysaccharide'],
                'varicella': ['chickenpox', 'chicken', 'pox'],
                'zoster': ['shingles', 'herpes', 'zoster'],
                'influenza': ['flu', 'influenza'],
                'meningococcal': ['meningitis', 'meningococcal'],
                'bcg': ['tuberculosis', 'tb'],
                'rotavirus': ['rotavirus', 'rota'],
                'hpv': ['papillomavirus', 'human', 'papilloma'],
                'covid': ['coronavirus', 'covid', 'sars'],
                'cholera': ['cholera', 'dukoral'],
                'typhoid': ['typhoid', 'typhus'],
                'yellow_fever': ['yellow', 'fever'],
                'japanese_encephalitis': ['japanese', 'encephalitis'],
                'rabies': ['rabies'],
                'anthrax': ['anthrax'],
                'smallpox': ['smallpox', 'variola']
            },
            
            # Brand name mappings
            'brand_mappings': {
                'boostrix': 'tdap',
                'adacel': 'tdap',
                'infanrix': 'dtap',
                'pediarix': 'dtap',
                'pentacel': 'dtap',
                'gardasil': 'hpv',
                'cervarix': 'hpv',
                'prevnar': 'pcv',
                'pneumovax': 'ppsv',
                'havrix': 'hepatitis_a',
                'vaqta': 'hepatitis_a',
                'engerix': 'hepatitis_b',
                'recombivax': 'hepatitis_b',
                'twinrix': 'hepatitis_ab',
                'varivax': 'varicella',
                'zostavax': 'zoster',
                'shingrix': 'zoster',
                'fluzone': 'influenza',
                'flumist': 'influenza',
                'menactra': 'meningococcal',
                'menveo': 'meningococcal',
                'rotateq': 'rotavirus',
                'rotarix': 'rotavirus',
                'dukoral': 'cholera',
                'vivotif': 'typhoid',
                'typhim': 'typhoid',
                'ixiaro': 'japanese_encephalitis',
                'imovax': 'rabies',
                'rabavert': 'rabies'
            },
            
            # Age/dose indicators
            'age_dose_patterns': {
                'infant': ['infant', 'baby', 'newborn'],
                'child': ['child', 'pediatric', 'paed'],
                'adult': ['adult', 'grown'],
                'booster': ['booster', 'boost', '2nd', '3rd', 'second', 'third'],
                'primary': ['primary', '1st', 'first', 'initial']
            }
        }
    
    def normalize_vaccine_name(self, name):
        """Advanced normalization for vaccine names."""
        if not name or pd.isna(name):
            return ""
        
        # Convert to lowercase and clean
        name = str(name).lower().strip()
        
        # Remove common non-essential words
        remove_words = ['vaccine', 'vaccination', 'immunization', 'shot', 'injection', 'dose']
        for word in remove_words:
            name = re.sub(rf'\b{word}\b', '', name)
        
        # Standardize separators
        name = re.sub(r'[/\-_\+]', ' ', name)
        
        # Remove extra punctuation and spaces
        name = re.sub(r'[^\w\s]', ' ', name)
        name = re.sub(r'\s+', ' ', name).strip()
        
        return name
    
    def extract_vaccine_components(self, name):
        """Extract vaccine components and characteristics."""
        normalized = self.normalize_vaccine_name(name)
        components = {
            'vaccine_types': [],
            'brands': [],
            'age_groups': [],
            'dose_info': [],
            'special_notes': []
        }
        
        # Check for vaccine types
        for vaccine_type, keywords in self.vaccine_rules['exact_medical_matches'].items():
            if any(keyword in normalized for keyword in keywords):
                components['vaccine_types'].append(vaccine_type)
        
        # Check for brands
        for brand, vaccine_type in self.vaccine_rules['brand_mappings'].items():
            if brand in normalized:
                components['brands'].append(brand)
                components['vaccine_types'].append(vaccine_type)
        
        # Check for age/dose indicators
        for category, keywords in self.vaccine_rules['age_dose_patterns'].items():
            if any(keyword in normalized for keyword in keywords):
                components[category.replace('_', '_') + '_info'] = True
                components['special_notes'].append(category)
        
        return components
    
    def calculate_smart_similarity(self, legacy_name, target_name):
        """Calculate intelligent similarity based on medical knowledge."""
        
        # Extract components
        legacy_components = self.extract_vaccine_components(legacy_name)
        target_components = self.extract_vaccine_components(target_name)
        
        # Base text similarity
        text_similarity = fuzz.token_set_ratio(
            self.normalize_vaccine_name(legacy_name),
            self.normalize_vaccine_name(target_name)
        )
        
        # Medical component matching
        component_score = 0
        
        # Vaccine type matching (most important)
        legacy_types = set(legacy_components['vaccine_types'])
        target_types = set(target_components['vaccine_types'])
        
        if legacy_types and target_types:
            type_overlap = len(legacy_types.intersection(target_types))
            type_total = len(legacy_types.union(target_types))
            component_score += (type_overlap / type_total) * 60  # Up to 60 points
        
        # Brand matching
        legacy_brands = set(legacy_components['brands'])
        target_brands = set(target_components['brands'])
        
        if legacy_brands and target_brands:
            if legacy_brands.intersection(target_brands):
                component_score += 20  # Exact brand match
        
        # Special characteristics matching
        legacy_notes = set(legacy_components['special_notes'])
        target_notes = set(target_components['special_notes'])
        
        if legacy_notes and target_notes:
            note_overlap = len(legacy_notes.intersection(target_notes))
            if note_overlap > 0:
                component_score += note_overlap * 5  # Up to 20 points
        
        # Combine scores (weighted average)
        final_score = (text_similarity * 0.4) + (component_score * 0.6)
        
        return min(100, final_score)
    
    def find_best_match(self, legacy_vaccine, indici_database):
        """Find the best match using smart rules."""
        
        legacy_name = legacy_vaccine.get('description', '')
        legacy_code = legacy_vaccine.get('code', '')
        
        if not legacy_name and not legacy_code:
            return None
        
        # Combine code and description for better matching
        search_text = f"{legacy_code} {legacy_name}".strip()
        
        best_match = None
        best_score = 0
        
        for vaccine in indici_database:
            # Create searchable text for target vaccine
            target_text = f"{vaccine['vaccine_code']} {vaccine['vaccine_name']} {vaccine['brand']} {vaccine['long_description']}"
            
            # Calculate smart similarity
            score = self.calculate_smart_similarity(search_text, target_text)
            
            if score > best_score:
                best_score = score
                best_match = {
                    'vaccine': vaccine,
                    'score': score,
                    'rationale': self._generate_rationale(search_text, target_text, score)
                }
        
        return best_match
    
    def _generate_rationale(self, legacy_text, target_text, score):
        """Generate rationale for the match."""
        
        legacy_components = self.extract_vaccine_components(legacy_text)
        target_components = self.extract_vaccine_components(target_text)
        
        rationale_parts = []
        
        # Vaccine type matching
        legacy_types = set(legacy_components['vaccine_types'])
        target_types = set(target_components['vaccine_types'])
        common_types = legacy_types.intersection(target_types)
        
        if common_types:
            rationale_parts.append(f"Matching vaccine types: {', '.join(common_types)}")
        
        # Brand matching
        legacy_brands = set(legacy_components['brands'])
        target_brands = set(target_components['brands'])
        common_brands = legacy_brands.intersection(target_brands)
        
        if common_brands:
            rationale_parts.append(f"Matching brands: {', '.join(common_brands)}")
        
        # Score interpretation
        if score >= 90:
            confidence = "Very high confidence"
        elif score >= 80:
            confidence = "High confidence"
        elif score >= 70:
            confidence = "Good confidence"
        elif score >= 60:
            confidence = "Moderate confidence"
        else:
            confidence = "Low confidence"
        
        rationale_parts.insert(0, f"{confidence} match (score: {score:.1f}%)")
        
        return " | ".join(rationale_parts)
    
    def process_all_vaccines(self):
        """Process all vaccines automatically using smart rules."""
        
        logger.info("Starting smart rule-based vaccine mapping...")
        
        # Initialize mapper
        self.mapper = VaccineMapper(EXCEL_FILE)
        if not self.mapper.load_data():
            raise Exception("Failed to load vaccine data")
        
        self.mapper.clean_and_normalize_data()
        self.mapper.initialize_required_mapping()
        
        # Apply existing mappings
        self.mapper.apply_overrides()
        self.mapper.exact_matching_pass()
        
        # Prepare Indici database
        indici_database = []
        for _, row in self.mapper.indici_df.iterrows():
            vaccine_record = {
                'vaccine_code': str(row[INDICI_COLUMNS['vaccine_code']]),
                'vaccine_name': str(row[INDICI_COLUMNS['vaccine_name']]),
                'brand': str(row[INDICI_COLUMNS['brand']]) if pd.notna(row[INDICI_COLUMNS['brand']]) else '',
                'long_description': str(row[INDICI_COLUMNS['long_description']]) if pd.notna(row[INDICI_COLUMNS['long_description']]) else ''
            }
            indici_database.append(vaccine_record)
        
        # Get unmapped vaccines
        unmapped_mask = (
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna()) |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == STATUS_FUZZY_HIGH) |
            (self.mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == STATUS_NEEDS_REVIEW)
        )
        
        unmapped_vaccines = self.mapper.required_mapping_df[unmapped_mask]
        total_to_process = len(unmapped_vaccines)
        
        logger.info(f"Processing {total_to_process} vaccines with smart rules...")
        
        # Counters
        auto_mapped = 0
        no_match = 0
        
        for idx, (row_idx, row) in enumerate(unmapped_vaccines.iterrows(), 1):
            legacy_vaccine = {
                'code': row[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                'description': row[REQUIRED_MAPPING_COLUMNS['legacy_description']]
            }
            
            logger.info(f"Processing {idx}/{total_to_process}: {legacy_vaccine['code']} - {legacy_vaccine['description'][:50]}...")
            
            # Find best match
            match_result = self.find_best_match(legacy_vaccine, indici_database)
            
            if match_result and match_result['score'] >= 70:
                # Auto-map with good confidence
                vaccine = match_result['vaccine']
                
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = vaccine['vaccine_code']
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = vaccine['vaccine_name']
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = vaccine['long_description']
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = "smart_auto_mapped"
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"Smart rule-based mapping: {match_result['rationale']}"
                
                auto_mapped += 1
                logger.info(f"✅ Auto-mapped to {vaccine['vaccine_name']} (score: {match_result['score']:.1f}%)")
                
            else:
                # No good match found
                self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
                if match_result:
                    self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = f"Best match found but low confidence: {match_result['rationale']}"
                else:
                    self.mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = "No suitable match found in standard vaccine database"
                
                no_match += 1
                logger.info(f"❌ No good match found")
        
        logger.info(f"Completed processing: {auto_mapped} auto-mapped, {no_match} no match")
        return auto_mapped, no_match
    
    def save_results(self):
        """Save results to Excel."""
        logger.info("Saving results to Excel...")
        return self.mapper.save_results()
    
    def generate_summary(self):
        """Generate final summary."""
        summary = self.mapper.get_mapping_summary()
        total = len(self.mapper.required_mapping_df)
        
        print("\n" + "=" * 60)
        print("🧠 SMART RULE-BASED MAPPING COMPLETED")
        print("=" * 60)
        print(f"Total vaccines processed: {total}")
        print()
        
        for status, count in summary.items():
            percentage = (count / total * 100) if total > 0 else 0
            status_display = {
                STATUS_EXACT: "✅ Exact matches",
                "smart_auto_mapped": "🧠 Smart rule-based mappings",
                STATUS_NO_MATCH: "❌ No matches found",
                STATUS_MANUAL_CONFIRMED: "👤 Previously confirmed",
                STATUS_MANUAL_OVERRIDE: "🔧 Manual overrides",
                'unmapped': "❓ Still unmapped"
            }.get(status, f"📊 {status}")
            
            print(f"{status_display}: {count} ({percentage:.1f}%)")
        
        # Calculate automation rate
        automated = summary.get(STATUS_EXACT, 0) + summary.get("smart_auto_mapped", 0)
        automation_rate = (automated / total * 100) if total > 0 else 0
        
        print(f"\n🎯 AUTOMATION ACHIEVED: {automated}/{total} vaccines ({automation_rate:.1f}%)")
        print("🚀 NO API REQUIRED - FULLY AUTOMATED!")
        print("=" * 60)

def main():
    """Main function."""
    
    print("🧠 SMART RULE-BASED VACCINE MAPPING")
    print("=" * 60)
    print("This system uses intelligent medical rules for automatic mapping")
    print("NO API required - fully automated with medical knowledge!")
    print()
    
    try:
        # Initialize mapper
        mapper = SmartRuleBasedMapper()
        
        # Process all vaccines
        auto_mapped, no_match = mapper.process_all_vaccines()
        
        # Save results
        if mapper.save_results():
            print("✅ Results saved successfully!")
        else:
            print("❌ Failed to save results")
            return
        
        # Generate summary
        mapper.generate_summary()
        
        print(f"\n📁 Results saved to: {EXCEL_FILE}")
        print("📋 Check the 'Required Mapping' sheet for all mappings")
        
    except Exception as e:
        logger.error(f"Smart mapping failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
