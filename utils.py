"""
Utility functions for the vaccine mapping system.
"""

import re
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def normalize_text(text: str) -> str:
    """
    Normalize text for matching: lowercase, trim, remove extra punctuation, collapse whitespace.
    
    Args:
        text: Input text to normalize
        
    Returns:
        Normalized text string
    """
    if pd.isna(text) or text is None:
        return ""
    
    # Convert to string and lowercase
    text = str(text).lower().strip()
    
    # Remove extra punctuation but keep basic separators
    text = re.sub(r'[^\w\s\-\(\)]', ' ', text)
    
    # Collapse multiple whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    return text

def create_search_variants(text: str) -> List[str]:
    """
    Create search variants for better matching.
    
    Args:
        text: Input text
        
    Returns:
        List of text variants for searching
    """
    if not text:
        return []
    
    variants = [text]
    
    # Remove parentheses content
    no_parens = re.sub(r'\([^)]*\)', '', text).strip()
    if no_parens and no_parens != text:
        variants.append(no_parens)
    
    # Remove common vaccine suffixes/prefixes
    common_terms = ['vaccine', 'vaccination', 'immunization', 'shot']
    for term in common_terms:
        if term in text:
            variant = text.replace(term, '').strip()
            if variant:
                variants.append(variant)
    
    return list(set(variants))

def safe_excel_read(file_path: str, sheet_name: str) -> pd.DataFrame:
    """
    Safely read Excel sheet with error handling.
    
    Args:
        file_path: Path to Excel file
        sheet_name: Name of sheet to read
        
    Returns:
        DataFrame or empty DataFrame if error
    """
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        logger.info(f"Successfully read sheet '{sheet_name}' with {len(df)} rows")
        return df
    except Exception as e:
        logger.error(f"Error reading sheet '{sheet_name}': {str(e)}")
        return pd.DataFrame()

def safe_excel_write(df: pd.DataFrame, file_path: str, sheet_name: str, mode: str = 'a') -> bool:
    """
    Safely write DataFrame to Excel sheet.
    
    Args:
        df: DataFrame to write
        file_path: Path to Excel file
        sheet_name: Name of sheet to write
        mode: Write mode ('a' for append, 'w' for overwrite)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with pd.ExcelWriter(file_path, mode=mode, if_sheet_exists='replace', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        logger.info(f"Successfully wrote {len(df)} rows to sheet '{sheet_name}'")
        return True
    except Exception as e:
        logger.error(f"Error writing to sheet '{sheet_name}': {str(e)}")
        return False

def load_overrides(file_path: str) -> pd.DataFrame:
    """
    Load override mappings from CSV file.
    
    Args:
        file_path: Path to overrides CSV file
        
    Returns:
        DataFrame with override mappings
    """
    try:
        if pd.io.common.file_exists(file_path):
            df = pd.read_csv(file_path)
            logger.info(f"Loaded {len(df)} override mappings")
            return df
        else:
            logger.info("No overrides file found, creating empty DataFrame")
            return pd.DataFrame(columns=['legacy_norm', 'current_norm', 'match_type', 'reason', 'confirmed_by', 'confirmed_on'])
    except Exception as e:
        logger.error(f"Error loading overrides: {str(e)}")
        return pd.DataFrame(columns=['legacy_norm', 'current_norm', 'match_type', 'reason', 'confirmed_by', 'confirmed_on'])

def save_override(legacy_norm: str, current_norm: str, match_type: str, reason: str, 
                 confirmed_by: str, file_path: str) -> bool:
    """
    Save a new override mapping to CSV file.
    
    Args:
        legacy_norm: Normalized legacy vaccine name
        current_norm: Normalized current vaccine name
        match_type: Type of match (manual_confirmed, manual_override, etc.)
        reason: Reason for the mapping
        confirmed_by: Who confirmed the mapping
        file_path: Path to overrides CSV file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        new_override = {
            'legacy_norm': legacy_norm,
            'current_norm': current_norm,
            'match_type': match_type,
            'reason': reason,
            'confirmed_by': confirmed_by,
            'confirmed_on': datetime.now().isoformat()
        }
        
        # Load existing overrides
        overrides_df = load_overrides(file_path)
        
        # Add new override
        overrides_df = pd.concat([overrides_df, pd.DataFrame([new_override])], ignore_index=True)
        
        # Save back to file
        overrides_df.to_csv(file_path, index=False)
        logger.info(f"Saved override: {legacy_norm} -> {current_norm}")
        return True
    except Exception as e:
        logger.error(f"Error saving override: {str(e)}")
        return False

def calculate_match_score(text1: str, text2: str) -> float:
    """
    Calculate similarity score between two texts using multiple methods.
    
    Args:
        text1: First text
        text2: Second text
        
    Returns:
        Similarity score between 0 and 100
    """
    from rapidfuzz import fuzz
    
    if not text1 or not text2:
        return 0.0
    
    # Use multiple fuzzy matching methods and take the maximum
    scores = [
        fuzz.ratio(text1, text2),
        fuzz.partial_ratio(text1, text2),
        fuzz.token_sort_ratio(text1, text2),
        fuzz.token_set_ratio(text1, text2)
    ]
    
    return max(scores)

def format_confidence_display(confidence: float) -> str:
    """
    Format confidence score for display.
    
    Args:
        confidence: Confidence score (0-100)
        
    Returns:
        Formatted confidence string
    """
    if confidence >= 90:
        return f"🟢 {confidence:.1f}% (High)"
    elif confidence >= 70:
        return f"🟡 {confidence:.1f}% (Medium)"
    else:
        return f"🔴 {confidence:.1f}% (Low)"
