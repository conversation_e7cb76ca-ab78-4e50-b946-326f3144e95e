"""
Pure LLM-based MCP Server for vaccine mapping.
This addresses the fuzzy logic inconsistency issue by using only LLM for suggestions.
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import os
from dotenv import load_dotenv
from openai import OpenAI

from vaccine_mapper import VaccineMapper
from config import *
from utils import *

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="🤖 LLM-Only Vaccine Mapping (MCP)",
    page_icon="💉",
    layout="wide"
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@st.cache_data
def load_mapping_data():
    """Load mapping data with caching."""
    mapper = VaccineMapper(EXCEL_FILE)
    if mapper.load_data():
        mapper.initialize_required_mapping()
        return mapper
    return None

@st.cache_data
def get_indici_database(mapper):
    """Get complete Indici database for LLM."""
    if mapper and not mapper.indici_df.empty:
        database = []
        for _, row in mapper.indici_df.iterrows():
            vaccine_record = {
                'vaccine_code': row[INDICI_COLUMNS['vaccine_code']],
                'vaccine_name': row[INDICI_COLUMNS['vaccine_name']],
                'brand': row[INDICI_COLUMNS['brand']],
                'long_description': row[INDICI_COLUMNS['long_description']]
            }
            database.append(vaccine_record)
        return database
    return []

def get_llm_suggestion(legacy_vaccine, indici_database):
    """Get LLM suggestion for a vaccine."""
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        return None
    
    try:
        client = OpenAI(api_key=api_key)
        
        # Create prompt
        prompt = f"""You are a medical expert specializing in vaccine identification and mapping. Find the best match for this legacy vaccine from the standard database.

LEGACY VACCINE TO MATCH:
- Code: {legacy_vaccine.get('code', 'N/A')}
- Description: {legacy_vaccine.get('description', 'N/A')}

STANDARD VACCINE DATABASE:
"""
        
        for i, vaccine in enumerate(indici_database, 1):
            prompt += f"""
{i}. Code: {vaccine.get('vaccine_code', 'N/A')} | Name: {vaccine.get('vaccine_name', 'N/A')} | Brand: {vaccine.get('brand', 'N/A')} | Description: {vaccine.get('long_description', 'N/A')}"""
        
        prompt += """

INSTRUCTIONS:
1. Search through the ENTIRE database above to find the best match
2. Consider vaccine names, brands, descriptions, and clinical equivalence
3. Look for semantic matches even if exact text doesn't match
4. Be consistent - same legacy vaccine should always map to same standard vaccine
5. Provide your assessment in the exact JSON format below

REQUIRED JSON RESPONSE FORMAT:
{
    "best_match": {
        "vaccine_code": "exact code from database or null",
        "vaccine_name": "exact name from database or null",
        "confidence": 0-100,
        "rationale": "detailed explanation of why this is the best match"
    },
    "alternative_matches": [
        {
            "vaccine_code": "alternative option code from database",
            "vaccine_name": "alternative option name from database", 
            "confidence": 0-100,
            "rationale": "why this could also be a match"
        }
    ],
    "recommendation": "ACCEPT/REVIEW/REJECT"
}

CONFIDENCE SCORING:
- 95-100: Virtually certain match (same vaccine, different naming)
- 85-94: High confidence match (likely same vaccine)
- 70-84: Moderate confidence (possible match, needs review)
- 50-69: Low confidence (uncertain match)
- 0-49: Poor match or no match

RECOMMENDATIONS:
- ACCEPT: Confidence ≥90, can be auto-applied
- REVIEW: Confidence 50-89, needs human review
- REJECT: Confidence <50, no suitable match found

CRITICAL: Always use exact codes and names from the database above. Be consistent in your mappings.

Respond ONLY with the JSON object, no additional text."""

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a medical expert specializing in vaccine identification. Always respond with valid JSON only."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        # Parse JSON response
        response_text = response.choices[0].message.content.strip()
        
        # Clean up response if it has markdown formatting
        if response_text.startswith('```json'):
            response_text = response_text.replace('```json', '').replace('```', '').strip()
        
        import json
        suggestion = json.loads(response_text)
        
        return suggestion
        
    except Exception as e:
        st.error(f"LLM Error: {str(e)}")
        return None

def save_mapping_decision(mapper, row_idx, decision_type, indici_code=None, 
                         indici_name=None, indici_description=None, comments=None):
    """Save a mapping decision and update the Excel file."""
    
    try:
        # Update the mapping dataframe
        if decision_type == "accept":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_MANUAL_CONFIRMED
        elif decision_type == "reject":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NO_MATCH
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = ""
        elif decision_type == "override":
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_MANUAL_OVERRIDE
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = indici_code or ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = indici_name or ""
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = indici_description or ""
        
        # Update comments
        if comments:
            existing_comments = mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']]
            if pd.notna(existing_comments) and existing_comments.strip():
                new_comments = f"{existing_comments} | Manual review: {comments}"
            else:
                new_comments = f"Manual review: {comments}"
            mapper.required_mapping_df.loc[row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = new_comments
        
        # Save to Excel
        success = mapper.save_results()
        
        return success
        
    except Exception as e:
        logger.error(f"Error saving mapping decision: {str(e)}")
        return False

def main():
    """Main Streamlit application."""
    
    st.title("🤖 LLM-Only Vaccine Mapping (MCP)")
    st.markdown("**Pure AI-based mapping without fuzzy logic inconsistencies**")
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        st.error("❌ OpenAI API key not found. Please check your .env file.")
        st.stop()
    
    # Load data
    mapper = load_mapping_data()
    
    if not mapper:
        st.error("❌ Failed to load mapping data. Please ensure vaccines.xlsx exists.")
        st.stop()
    
    # Get items needing review (including fuzzy matches to be re-evaluated)
    review_statuses = [STATUS_NEEDS_REVIEW, STATUS_FUZZY_HIGH, '']
    review_mask = mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isin(review_statuses) | \
                  mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna()
    
    review_items = mapper.required_mapping_df[review_mask].copy()
    
    if review_items.empty:
        st.success("🎉 All vaccines have been reviewed!")
        st.stop()
    
    # Progress indicator
    total_items = len(review_items)
    st.subheader(f"📋 LLM Review: {total_items} items remaining")
    
    # Item selector
    if total_items > 1:
        item_index = st.selectbox(
            "Select item to review:",
            range(total_items),
            format_func=lambda x: f"Item {x+1}: {review_items.iloc[x][REQUIRED_MAPPING_COLUMNS['legacy_description']][:50]}..."
        )
    else:
        item_index = 0
    
    current_item = review_items.iloc[item_index]
    current_row_idx = review_items.index[item_index]
    
    # Display current item
    st.markdown("---")
    st.subheader(f"🔍 Reviewing Item {item_index + 1} of {total_items}")
    
    # Legacy vaccine information
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📋 Legacy Vaccine")
        st.write(f"**Code:** {current_item[REQUIRED_MAPPING_COLUMNS['legacy_code']]}")
        st.write(f"**Description:** {current_item[REQUIRED_MAPPING_COLUMNS['legacy_description']]}")
        
        current_status = current_item[REQUIRED_MAPPING_COLUMNS['mapping_status']]
        if pd.notna(current_status) and current_status:
            if current_status == STATUS_FUZZY_HIGH:
                st.warning(f"**Previous Status:** {current_status} (inconsistent - needs LLM review)")
            else:
                st.write(f"**Status:** {current_status}")
    
    with col2:
        st.markdown("### 🤖 LLM Analysis")
        
        # Get LLM suggestion
        if st.button("🔍 Get LLM Suggestion", key=f"llm_btn_{current_row_idx}"):
            with st.spinner("Analyzing with AI..."):
                legacy_vaccine = {
                    'code': current_item[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                    'description': current_item[REQUIRED_MAPPING_COLUMNS['legacy_description']]
                }
                
                indici_database = get_indici_database(mapper)
                suggestion = get_llm_suggestion(legacy_vaccine, indici_database)
                
                if suggestion:
                    st.session_state[f'suggestion_{current_row_idx}'] = suggestion
                else:
                    st.error("Failed to get LLM suggestion")
        
        # Display suggestion if available
        if f'suggestion_{current_row_idx}' in st.session_state:
            suggestion = st.session_state[f'suggestion_{current_row_idx}']
            best_match = suggestion.get('best_match', {})
            
            if best_match.get('vaccine_code'):
                confidence = best_match.get('confidence', 0)
                st.success(f"**Suggested Match** (Confidence: {confidence}%)")
                st.write(f"**Code:** {best_match.get('vaccine_code')}")
                st.write(f"**Name:** {best_match.get('vaccine_name')}")
                st.write(f"**Rationale:** {best_match.get('rationale', 'N/A')}")
                
                # Show alternatives
                alternatives = suggestion.get('alternative_matches', [])
                if alternatives:
                    st.write("**Alternatives:**")
                    for alt in alternatives[:2]:
                        st.write(f"- {alt.get('vaccine_name')} ({alt.get('confidence', 0)}%)")
            else:
                st.warning("No suitable match found by LLM")
    
    # Decision interface
    st.markdown("---")
    st.subheader("✅ Make Decision")
    
    decision_type = st.radio(
        "Choose action:",
        ["llm_accept", "reject", "override"],
        format_func=lambda x: {
            "llm_accept": "✅ Accept LLM suggestion",
            "reject": "❌ Reject - no suitable match",
            "override": "🔧 Override with custom mapping"
        }[x]
    )
    
    # Get LLM suggestion for accept option
    selected_vaccine = None
    if decision_type == "llm_accept":
        if f'suggestion_{current_row_idx}' in st.session_state:
            suggestion = st.session_state[f'suggestion_{current_row_idx}']
            best_match = suggestion.get('best_match', {})
            if best_match.get('vaccine_code'):
                # Find full vaccine info
                indici_database = get_indici_database(mapper)
                for vaccine in indici_database:
                    if vaccine['vaccine_code'] == best_match['vaccine_code']:
                        selected_vaccine = vaccine
                        break
        
        if not selected_vaccine:
            st.warning("Please get LLM suggestion first")
    
    # Override interface
    elif decision_type == "override":
        st.markdown("#### 🔧 Select Custom Mapping")
        
        indici_database = get_indici_database(mapper)
        
        if indici_database:
            # Search functionality
            search_term = st.text_input("🔍 Search vaccines:", placeholder="Type to search...")
            
            if search_term:
                filtered_options = [opt for opt in indici_database 
                                  if search_term.lower() in f"{opt['vaccine_code']} {opt['vaccine_name']} {opt['brand']}".lower()]
            else:
                filtered_options = indici_database
            
            if filtered_options:
                selected_idx = st.selectbox(
                    "Select vaccine:",
                    range(len(filtered_options)),
                    format_func=lambda x: f"{filtered_options[x]['vaccine_code']} - {filtered_options[x]['vaccine_name']} ({filtered_options[x]['brand']})"
                )
                selected_vaccine = filtered_options[selected_idx]
                
                # Show selected vaccine details
                st.write(f"**Selected:** {selected_vaccine['vaccine_name']}")
                st.write(f"**Description:** {selected_vaccine['long_description']}")
            else:
                st.warning("No vaccines found matching search term")
        else:
            st.error("No vaccines available")
    
    # Comments
    review_comments = st.text_area(
        "💬 Review comments (optional):",
        placeholder="Add any notes about this decision..."
    )
    
    # Action buttons
    if st.button("💾 Save Decision", type="primary"):
        if decision_type == "override" and not selected_vaccine:
            st.error("Please select a vaccine for override")
        elif decision_type == "llm_accept" and not selected_vaccine:
            st.error("Please get LLM suggestion first")
        else:
            # Save the decision
            success = save_mapping_decision(
                mapper=mapper,
                row_idx=current_row_idx,
                decision_type="accept" if decision_type == "llm_accept" else decision_type,
                indici_code=selected_vaccine['vaccine_code'] if selected_vaccine else None,
                indici_name=selected_vaccine['vaccine_name'] if selected_vaccine else None,
                indici_description=selected_vaccine['long_description'] if selected_vaccine else None,
                comments=review_comments
            )
            
            if success:
                st.success(f"✅ Decision saved successfully!")
                st.balloons()
                
                # Clear cache and rerun to refresh data
                st.cache_data.clear()
                st.rerun()
            else:
                st.error("❌ Failed to save decision")

if __name__ == "__main__":
    main()
