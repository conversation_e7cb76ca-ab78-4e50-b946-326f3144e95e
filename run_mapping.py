#!/usr/bin/env python3
"""
Main automation script for vaccine data mapping pipeline.

Workflow:
1. Load and clean vaccine data from Excel workbook
2. Apply manual override mappings
3. Perform exact matching pass
4. Perform fuzzy matching with high-confidence auto-mapping
5. Use LLM for ambiguous cases
6. Save results and launch MCP review UI for remaining items

Usage:
    python run_mapping.py [--no-llm] [--no-ui]
    
Arguments:
    --no-llm: Skip LLM processing
    --no-ui: Don't launch the review UI automatically
"""

import argparse
import sys
import os
import logging
import subprocess
from pathlib import Path

from vaccine_mapper import VaccineMapper
from llm_assistant import LLMVaccineAssistant
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('vaccine_mapping.log')
    ]
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if required files exist."""
    
    if not os.path.exists(EXCEL_FILE):
        logger.error(f"Excel file '{EXCEL_FILE}' not found in current directory")
        return False
    
    logger.info(f"Found Excel file: {EXCEL_FILE}")
    return True

def run_mapping_pipeline(use_llm: bool = True) -> bool:
    """
    Run the complete mapping pipeline.
    
    Args:
        use_llm: Whether to use LLM for suggestions
        
    Returns:
        True if successful, False otherwise
    """
    
    logger.info("=" * 60)
    logger.info("STARTING VACCINE MAPPING PIPELINE")
    logger.info("=" * 60)
    
    # Initialize mapper
    mapper = VaccineMapper(EXCEL_FILE)
    
    # Step 1: Load data
    logger.info("Step 1: Loading data from Excel workbook...")
    if not mapper.load_data():
        logger.error("Failed to load data")
        return False
    
    # Step 2: Clean and normalize
    logger.info("Step 2: Cleaning and normalizing data...")
    mapper.clean_and_normalize_data()
    
    # Step 3: Initialize Required Mapping sheet
    logger.info("Step 3: Initializing Required Mapping sheet...")
    mapper.initialize_required_mapping()
    
    # Step 4: Apply overrides
    logger.info("Step 4: Applying manual overrides...")
    mapper.apply_overrides()
    
    # Step 5: Exact matching
    logger.info("Step 5: Performing exact matching...")
    mapper.exact_matching_pass()
    
    # Step 6: Skip fuzzy matching (using LLM-only approach)
    logger.info("Step 6: Skipping fuzzy matching (using LLM-only approach for consistency)...")
    mapper.fuzzy_matching_pass()
    
    # Step 7: LLM processing (optional)
    if use_llm:
        logger.info("Step 7: Processing with LLM assistant...")
        llm_assistant = LLMVaccineAssistant()
        if llm_assistant.client:
            llm_assistant.batch_process_unmapped(mapper)
        else:
            logger.warning("LLM assistant not available - skipping LLM processing")
    else:
        logger.info("Step 7: Skipping LLM processing (disabled)")
    
    # Step 8: Save results
    logger.info("Step 8: Saving results...")
    if not mapper.save_results():
        logger.error("Failed to save results")
        return False
    
    # Step 9: Print summary
    logger.info("Step 9: Generating summary...")
    summary = mapper.get_mapping_summary()
    
    logger.info("=" * 60)
    logger.info("MAPPING PIPELINE COMPLETED")
    logger.info("=" * 60)
    
    print("\n" + "=" * 60)
    print("VACCINE MAPPING SUMMARY")
    print("=" * 60)
    
    total_vaccines = len(mapper.required_mapping_df)
    print(f"Total vaccines processed: {total_vaccines}")
    print()
    
    for status, count in summary.items():
        status_display = {
            STATUS_EXACT: "✅ Exact matches",
            STATUS_FUZZY_HIGH: "🔍 High-confidence fuzzy matches", 
            STATUS_LLM_SUGGESTED: "🤖 LLM suggested matches",
            STATUS_NEEDS_REVIEW: "⚠️  Needs human review",
            STATUS_MANUAL_CONFIRMED: "👤 Manually confirmed",
            STATUS_MANUAL_OVERRIDE: "🔧 Manual overrides",
            STATUS_NO_MATCH: "❌ No matches found",
            'unmapped': "❓ Unmapped"
        }.get(status, f"📊 {status}")
        
        percentage = (count / total_vaccines * 100) if total_vaccines > 0 else 0
        print(f"{status_display}: {count} ({percentage:.1f}%)")
    
    # Calculate review needed
    review_statuses = [STATUS_NEEDS_REVIEW, STATUS_LLM_SUGGESTED]
    review_needed = sum(summary.get(status, 0) for status in review_statuses)
    review_needed += summary.get('unmapped', 0)
    
    print()
    if review_needed > 0:
        print(f"🔍 {review_needed} vaccines need human review")
        print("💡 Run the MCP review UI: streamlit run mcp_server.py")
    else:
        print("🎉 All vaccines have been successfully mapped!")
    
    print("=" * 60)
    
    return True

def launch_review_ui():
    """Launch the Streamlit review UI."""
    
    try:
        logger.info("Launching MCP review UI...")
        
        # Check if streamlit is available
        result = subprocess.run(['streamlit', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logger.error("Streamlit not found. Please install with: pip install streamlit")
            return False
        
        # Launch streamlit app
        subprocess.Popen(['streamlit', 'run', 'mcp_server.py'])
        logger.info("MCP review UI launched successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to launch review UI: {str(e)}")
        return False

def main():
    """Main entry point."""
    
    parser = argparse.ArgumentParser(
        description="Vaccine Data Mapping Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python run_mapping.py                    # Full pipeline with LLM and UI
    python run_mapping.py --no-llm          # Skip LLM processing
    python run_mapping.py --no-ui           # Don't launch UI automatically
    python run_mapping.py --no-llm --no-ui  # Basic matching only
        """
    )
    
    parser.add_argument('--no-llm', action='store_true',
                       help='Skip LLM processing for ambiguous matches')
    parser.add_argument('--no-ui', action='store_true',
                       help='Don\'t launch the review UI automatically')
    
    args = parser.parse_args()
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Run mapping pipeline
    success = run_mapping_pipeline(use_llm=not args.no_llm)
    
    if not success:
        logger.error("Mapping pipeline failed")
        sys.exit(1)
    
    # Launch review UI if requested
    if not args.no_ui:
        # Check if there are items needing review
        mapper = VaccineMapper(EXCEL_FILE)
        mapper.load_data()
        mapper.initialize_required_mapping()
        
        review_mask = mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isin([
            STATUS_NEEDS_REVIEW, STATUS_LLM_SUGGESTED, ''
        ]) | mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna()
        
        review_count = review_mask.sum()
        
        if review_count > 0:
            print(f"\n🚀 Launching review UI for {review_count} items...")
            launch_review_ui()
        else:
            print("\n✅ No items need review - skipping UI launch")
    
    logger.info("Pipeline completed successfully")

if __name__ == "__main__":
    main()
