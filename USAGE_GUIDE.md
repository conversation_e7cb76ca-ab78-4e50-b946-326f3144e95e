# Vaccine Mapping System - Usage Guide

## 🚀 Quick Start

### 1. Setup (One-time)

```bash
# Run the automated setup
python setup.py

# OR manually:
python -m venv .venv
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

### 2. Configure Environment

Edit `.env` file and add your OpenAI API key:
```
OPENAI_API_KEY=your_actual_api_key_here
```

### 3. Run the Complete Pipeline

```bash
# Full automated pipeline with LLM and review UI
python run_mapping.py
```

That's it! The system will:
1. Process your `vaccines.xlsx` file
2. Perform automated matching
3. Launch a web interface for reviewing ambiguous cases

## 📊 What the System Does

### Automated Processing
- **Exact Matching**: Finds perfect text matches between legacy and standard vaccines
- **Fuzzy Matching**: Uses similarity algorithms to find likely matches (≥90% confidence)
- **LLM Suggestions**: Uses GPT-4 to analyze ambiguous cases semantically
- **Deduplication**: Removes exact duplicates while preserving dose variants

### Human Review Interface
- **Web-based UI**: Streamlit interface for reviewing suggestions
- **Accept/Reject/Override**: Three options for each mapping decision
- **Progress Tracking**: Shows completion status and remaining items
- **Persistent Storage**: Saves decisions for future runs

## 🎯 Expected Results

Based on your current data (471 vaccines to map):
- **Exact matches**: ~18 vaccines (4%)
- **High-confidence fuzzy**: ~177 vaccines (38%)
- **Needs review**: ~276 vaccines (58%)

The system significantly reduces manual work by automatically handling 42% of mappings.

## 📋 Command Line Options

```bash
# Full pipeline (recommended)
python run_mapping.py

# Skip LLM processing (faster, but less intelligent suggestions)
python run_mapping.py --no-llm

# Don't auto-launch review UI
python run_mapping.py --no-ui

# Basic matching only (exact + fuzzy)
python run_mapping.py --no-llm --no-ui

# Launch review UI separately
streamlit run mcp_server.py
```

## 🔍 Review Interface Guide

### Navigation
- **Item Selector**: Choose which vaccine to review
- **Progress Bar**: Shows overall completion status
- **Search**: Find specific vaccines in the standard list

### Decision Options

1. **✅ Accept**: Confirm the suggested mapping
   - Use when the suggestion looks correct
   - Saves to overrides for future consistency

2. **❌ Reject**: Mark as no suitable match
   - Use when no standard vaccine matches the legacy one
   - Marks the vaccine as unmappable

3. **🔧 Override**: Choose a different mapping
   - Use when you know the correct mapping
   - Search and select from the standard vaccine list
   - Saves your decision for future runs

### Best Practices

- **Review Comments**: Read LLM rationale before deciding
- **Check Descriptions**: Compare full vaccine descriptions
- **Consider Brands**: Same vaccine may have different brand names
- **Use Search**: Type keywords to find vaccines quickly

## 📁 Output Files

### Updated Excel File
Your `vaccines.xlsx` file will be updated with:
- **NIR / Indici Vaccine Code**: Mapped vaccine code
- **IndiciVaccineName**: Mapped vaccine name
- **IndiciVaccDescription**: Mapped description
- **Mapping Status**: How the mapping was determined
- **Comments**: Rationale and notes

### Override File
`overrides.csv` contains your manual decisions:
- Ensures consistency across future runs
- Can be edited manually if needed
- Tracks who made decisions and when

## 🔧 Troubleshooting

### Common Issues

**"Excel file not found"**
- Ensure `vaccines.xlsx` is in the project directory
- Check file name spelling

**"No module named 'X'"**
- Activate virtual environment: `.venv\Scripts\activate`
- Install dependencies: `pip install -r requirements.txt`

**"LLM processing failed"**
- Check OpenAI API key in `.env` file
- Verify API key has credits
- Run without LLM: `python run_mapping.py --no-llm`

**"Streamlit not found"**
- Install streamlit: `pip install streamlit`
- Or run: `python -m streamlit run mcp_server.py`

### Performance Tips

- **Large datasets**: Use `--no-llm` for faster processing
- **Limited API credits**: Process in batches
- **Network issues**: Run locally without LLM first

## 📈 Monitoring Progress

### Log Files
- `vaccine_mapping.log`: Detailed execution logs
- Check for errors and processing statistics

### Status Meanings
- `exact`: Perfect text match found
- `fuzzy_high`: High similarity match (≥90%)
- `llm_suggested`: AI recommended with confidence ≥85%
- `needs_review`: Requires human decision
- `manual_confirmed`: Human approved suggestion
- `manual_override`: Human chose different mapping
- `no_match`: No suitable mapping exists

## 🔄 Re-running the System

The system is designed to be run multiple times:
- **Overrides preserved**: Manual decisions are remembered
- **Incremental processing**: Only processes unmapped items
- **Safe updates**: Original data is preserved

To start fresh:
1. Delete `overrides.csv`
2. Clear mapping columns in Excel
3. Run the pipeline again

## 💡 Tips for Better Results

### Data Quality
- Ensure vaccine descriptions are complete
- Check for typos in legacy data
- Standardize abbreviations when possible

### Review Strategy
- Start with high-confidence suggestions
- Group similar vaccines together
- Use brand names as additional context
- When in doubt, reject rather than guess

### Efficiency
- Review in batches of 10-20 items
- Take breaks to maintain accuracy
- Use the search function extensively
- Save frequently (automatic in web UI)

## 🆘 Getting Help

1. **Check logs**: Review `vaccine_mapping.log` for errors
2. **Test system**: Run `python test_system.py`
3. **Verify data**: Ensure Excel sheets have correct structure
4. **API issues**: Check OpenAI API status and credits

## 📞 Support

For technical issues:
1. Check this guide first
2. Review error logs
3. Test with sample data
4. Document exact error messages and steps to reproduce
