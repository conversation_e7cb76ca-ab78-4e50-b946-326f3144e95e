#!/usr/bin/env python3
"""
Setup script for the vaccine mapping system.
Creates virtual environment and installs dependencies.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        if platform.system() == "Windows":
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def main():
    """Main setup function."""
    
    print("🚀 Setting up Vaccine Mapping System")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version} detected")
    
    # Check if we're already in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        # Create virtual environment
        venv_path = Path(".venv")
        if not venv_path.exists():
            if not run_command("python -m venv .venv", "Creating virtual environment"):
                sys.exit(1)
        else:
            print("✅ Virtual environment already exists")
        
        # Provide activation instructions
        if platform.system() == "Windows":
            activate_cmd = ".venv\\Scripts\\activate"
        else:
            activate_cmd = "source .venv/bin/activate"
        
        print(f"\n📝 To activate the virtual environment, run:")
        print(f"   {activate_cmd}")
        print(f"\n📝 Then run this setup script again from within the virtual environment")
        print(f"   python setup.py")
        return
    
    print("✅ Running in virtual environment")
    
    # Upgrade pip
    if not run_command("python -m pip install --upgrade pip", "Upgrading pip"):
        print("⚠️  Warning: Failed to upgrade pip, continuing anyway...")
    
    # Install requirements
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        sys.exit(1)
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        template_file = Path(".env.template")
        if template_file.exists():
            template_file.rename(env_file)
            print("✅ Created .env file from template")
            print("📝 Please edit .env file and add your OpenAI API key")
        else:
            # Create basic .env file
            with open(".env", "w") as f:
                f.write("# OpenAI API Key for LLM-assisted matching\n")
                f.write("OPENAI_API_KEY=your_openai_api_key_here\n")
            print("✅ Created basic .env file")
            print("📝 Please edit .env file and add your OpenAI API key")
    else:
        print("✅ .env file already exists")
    
    # Check for vaccines.xlsx
    excel_file = Path("vaccines.xlsx")
    if not excel_file.exists():
        print("⚠️  vaccines.xlsx not found in current directory")
        print("📝 Please place your Excel file in this directory before running the mapping pipeline")
    else:
        print("✅ vaccines.xlsx found")
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("=" * 50)
    
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your OpenAI API key")
    print("2. Ensure vaccines.xlsx is in the current directory")
    print("3. Run the mapping pipeline: python run_mapping.py")
    print("4. Review mappings in the UI: streamlit run mcp_server.py")
    
    print("\n📚 Usage examples:")
    print("   python run_mapping.py                    # Full pipeline")
    print("   python run_mapping.py --no-llm          # Skip LLM processing")
    print("   python run_mapping.py --no-ui           # Don't launch UI")
    print("   streamlit run mcp_server.py             # Launch review UI only")

if __name__ == "__main__":
    main()
