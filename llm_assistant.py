"""
LLM integration module for vaccine matching suggestions.

Uses OpenAI GPT-4 to provide intelligent matching suggestions for ambiguous vaccine mappings.
"""

import json
import logging
from typing import Dict, List, Optional, Tuple
import openai
from openai import OpenAI

from config import *

logger = logging.getLogger(__name__)

class LLMVaccineAssistant:
    """LLM assistant for vaccine matching suggestions."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the LLM assistant.
        
        Args:
            api_key: OpenAI API key (uses config default if None)
        """
        self.api_key = api_key or OPENAI_API_KEY
        if not self.api_key:
            logger.warning("No OpenAI API key provided. LLM suggestions will be disabled.")
            self.client = None
        else:
            self.client = OpenAI(api_key=self.api_key)
    
    def create_matching_prompt(self, legacy_vaccine: Dict, candidates: List[Dict]) -> str:
        """
        Create a structured prompt for vaccine matching.
        
        Args:
            legacy_vaccine: Legacy vaccine information
            candidates: List of candidate Indici vaccines
            
        Returns:
            Formatted prompt string
        """
        prompt = f"""You are a medical expert specializing in vaccine identification and mapping. Your task is to determine if any of the provided candidate vaccines match the legacy vaccine record.

LEGACY VACCINE TO MATCH:
- Code: {legacy_vaccine.get('code', 'N/A')}
- Description: {legacy_vaccine.get('description', 'N/A')}

CANDIDATE VACCINES FROM STANDARD LIST:
"""
        
        for i, candidate in enumerate(candidates, 1):
            prompt += f"""
{i}. Vaccine Code: {candidate.get('vaccine_code', 'N/A')}
   Vaccine Name: {candidate.get('vaccine_name', 'N/A')}
   Brand: {candidate.get('brand', 'N/A')}
   Description: {candidate.get('long_description', 'N/A')}
   Similarity Score: {candidate.get('similarity_score', 0):.1f}%
"""
        
        prompt += """
INSTRUCTIONS:
1. Analyze the legacy vaccine against each candidate
2. Consider vaccine names, brands, descriptions, and clinical equivalence
3. Look for semantic matches even if exact text doesn't match
4. Consider common abbreviations, brand names, and medical terminology
5. Provide your assessment in the exact JSON format below

REQUIRED JSON RESPONSE FORMAT:
{
    "best_match": {
        "vaccine_code": "code of best matching vaccine or null",
        "vaccine_name": "name of best matching vaccine or null",
        "confidence": 0-100,
        "rationale": "detailed explanation of why this is the best match"
    },
    "auto_accept": true/false,
    "alternative_matches": [
        {
            "vaccine_code": "alternative option code",
            "vaccine_name": "alternative option name", 
            "confidence": 0-100,
            "rationale": "why this could also be a match"
        }
    ],
    "recommendation": "ACCEPT/REVIEW/REJECT"
}

CONFIDENCE SCORING:
- 90-100: Virtually certain match (same vaccine, different naming)
- 80-89: High confidence match (likely same vaccine)
- 70-79: Moderate confidence (possible match, needs review)
- 60-69: Low confidence (uncertain match)
- 0-59: Poor match or no match

RECOMMENDATIONS:
- ACCEPT: Confidence ≥85, can be auto-applied
- REVIEW: Confidence 60-84, needs human review
- REJECT: Confidence <60, no suitable match found

Respond ONLY with the JSON object, no additional text."""

        return prompt
    
    def get_vaccine_suggestion(self, legacy_vaccine: Dict, candidates: List[Dict]) -> Optional[Dict]:
        """
        Get LLM suggestion for vaccine matching.
        
        Args:
            legacy_vaccine: Legacy vaccine information
            candidates: List of candidate vaccines
            
        Returns:
            LLM response dictionary or None if error
        """
        if not self.client:
            logger.warning("LLM client not initialized. Skipping LLM suggestion.")
            return None
        
        if not candidates:
            logger.info("No candidates provided for LLM evaluation")
            return None
        
        try:
            prompt = self.create_matching_prompt(legacy_vaccine, candidates)
            
            response = self.client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are a medical expert specializing in vaccine identification. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=OPENAI_MAX_TOKENS,
                temperature=OPENAI_TEMPERATURE
            )
            
            # Parse JSON response
            response_text = response.choices[0].message.content.strip()
            
            # Clean up response if it has markdown formatting
            if response_text.startswith('```json'):
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            
            suggestion = json.loads(response_text)
            
            # Validate response structure
            required_keys = ['best_match', 'auto_accept', 'recommendation']
            if not all(key in suggestion for key in required_keys):
                logger.error("LLM response missing required keys")
                return None
            
            logger.info(f"LLM suggestion: {suggestion['recommendation']} "
                       f"(confidence: {suggestion['best_match'].get('confidence', 0)})")
            
            return suggestion
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM JSON response: {str(e)}")
            logger.error(f"Raw response: {response_text}")
            return None
        except Exception as e:
            logger.error(f"Error getting LLM suggestion: {str(e)}")
            return None
    
    def process_llm_suggestion(self, suggestion: Dict, legacy_row_idx: int, 
                              required_mapping_df, candidates: List[Dict]) -> str:
        """
        Process LLM suggestion and update mapping dataframe.
        
        Args:
            suggestion: LLM suggestion dictionary
            legacy_row_idx: Index of legacy row in mapping dataframe
            required_mapping_df: Required mapping dataframe
            candidates: Original candidates list
            
        Returns:
            Status to set for the mapping
        """
        best_match = suggestion.get('best_match', {})
        confidence = best_match.get('confidence', 0)
        auto_accept = suggestion.get('auto_accept', False)
        recommendation = suggestion.get('recommendation', 'REVIEW')
        
        # Find the full candidate information
        matched_candidate = None
        if best_match.get('vaccine_code'):
            for candidate in candidates:
                if candidate['vaccine_code'] == best_match['vaccine_code']:
                    matched_candidate = candidate
                    break
        
        if matched_candidate and confidence >= LLM_CONFIDENCE_THRESHOLD and auto_accept:
            # High confidence - auto-apply
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = \
                matched_candidate['vaccine_code']
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = \
                matched_candidate['vaccine_name']
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = \
                matched_candidate['long_description']
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                f"LLM suggested (confidence: {confidence}%): {best_match.get('rationale', '')}"
            
            return STATUS_LLM_SUGGESTED
        
        else:
            # Needs review - store suggestion in comments
            comment_parts = [f"LLM analysis (confidence: {confidence}%)"]
            
            if best_match.get('vaccine_code'):
                comment_parts.append(f"Suggested: {best_match.get('vaccine_name', 'N/A')} "
                                   f"({best_match.get('vaccine_code', 'N/A')})")
            
            if best_match.get('rationale'):
                comment_parts.append(f"Rationale: {best_match.get('rationale', '')}")
            
            # Add alternative matches
            alternatives = suggestion.get('alternative_matches', [])
            if alternatives:
                alt_text = "; ".join([f"{alt.get('vaccine_name', 'N/A')} "
                                    f"({alt.get('confidence', 0)}%)" 
                                    for alt in alternatives[:2]])
                comment_parts.append(f"Alternatives: {alt_text}")
            
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                " | ".join(comment_parts)
            
            return STATUS_NEEDS_REVIEW
    
    def batch_process_unmapped(self, vaccine_mapper) -> int:
        """
        Process all unmapped vaccines through LLM.
        
        Args:
            vaccine_mapper: VaccineMapper instance
            
        Returns:
            Number of vaccines processed
        """
        if not self.client:
            logger.warning("LLM client not available for batch processing")
            return 0
        
        # Get unmapped rows
        unmapped_mask = (vaccine_mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') | \
                       (vaccine_mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
        
        processed_count = 0
        
        for idx, row in vaccine_mapper.required_mapping_df[unmapped_mask].iterrows():
            legacy_vaccine = {
                'code': row[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                'description': row[REQUIRED_MAPPING_COLUMNS['legacy_description']]
            }
            
            # Get candidates for this legacy vaccine
            legacy_desc_norm = row['legacy_description_normalized']
            candidates = vaccine_mapper.get_candidates_for_llm(legacy_desc_norm)
            
            if candidates:
                # Get LLM suggestion
                suggestion = self.get_vaccine_suggestion(legacy_vaccine, candidates)
                
                if suggestion:
                    # Process the suggestion
                    status = self.process_llm_suggestion(
                        suggestion, idx, vaccine_mapper.required_mapping_df, candidates
                    )
                    vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = status
                    processed_count += 1
                else:
                    # LLM failed - mark for review
                    vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NEEDS_REVIEW
                    vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                        "LLM processing failed - manual review required"
            else:
                # No candidates found
                vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NEEDS_REVIEW
                vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                    "No suitable candidates found for matching"
        
        logger.info(f"LLM processed {processed_count} unmapped vaccines")
        return processed_count
