"""
LLM integration module for vaccine matching suggestions.

Uses OpenAI GPT-4 to provide intelligent matching suggestions for ambiguous vaccine mappings.
"""

import json
import logging
from typing import Dict, List, Optional, Tuple
import openai
from openai import OpenAI

from config import *

logger = logging.getLogger(__name__)

class LLMVaccineAssistant:
    """LLM assistant for vaccine matching suggestions."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the LLM assistant.
        
        Args:
            api_key: OpenAI API key (uses config default if None)
        """
        self.api_key = api_key or OPENAI_API_KEY
        if not self.api_key:
            logger.warning("No OpenAI API key provided. LLM suggestions will be disabled.")
            self.client = None
        else:
            self.client = OpenAI(api_key=self.api_key)
    
    def create_matching_prompt(self, legacy_vaccine: Dict, indici_vaccines: List[Dict]) -> str:
        """
        Create a structured prompt for vaccine matching against full database.

        Args:
            legacy_vaccine: Legacy vaccine information
            indici_vaccines: Complete list of standard vaccines

        Returns:
            Formatted prompt string
        """
        prompt = f"""You are a medical expert specializing in vaccine identification and mapping. Your task is to find the best match for a legacy vaccine from a comprehensive standard vaccine database.

LEGACY VACCINE TO MATCH:
- Code: {legacy_vaccine.get('code', 'N/A')}
- Description: {legacy_vaccine.get('description', 'N/A')}

STANDARD VACCINE DATABASE:
"""

        for i, vaccine in enumerate(indici_vaccines, 1):
            prompt += f"""
{i}. Code: {vaccine.get('vaccine_code', 'N/A')} | Name: {vaccine.get('vaccine_name', 'N/A')} | Brand: {vaccine.get('brand', 'N/A')} | Description: {vaccine.get('long_description', 'N/A')}"""
        
        prompt += """

INSTRUCTIONS:
1. Search through the ENTIRE database above to find the best match
2. Consider vaccine names, brands, descriptions, and clinical equivalence
3. Look for semantic matches even if exact text doesn't match
4. Consider common abbreviations, brand names, and medical terminology
5. Be consistent - same legacy vaccine should always map to same standard vaccine
6. Provide your assessment in the exact JSON format below

REQUIRED JSON RESPONSE FORMAT:
{
    "best_match": {
        "vaccine_code": "exact code from database or null",
        "vaccine_name": "exact name from database or null",
        "confidence": 0-100,
        "rationale": "detailed explanation of why this is the best match"
    },
    "auto_accept": true/false,
    "alternative_matches": [
        {
            "vaccine_code": "alternative option code from database",
            "vaccine_name": "alternative option name from database",
            "confidence": 0-100,
            "rationale": "why this could also be a match"
        }
    ],
    "recommendation": "ACCEPT/REVIEW/REJECT"
}

CONFIDENCE SCORING:
- 95-100: Virtually certain match (same vaccine, different naming)
- 85-94: High confidence match (likely same vaccine)
- 70-84: Moderate confidence (possible match, needs review)
- 50-69: Low confidence (uncertain match)
- 0-49: Poor match or no match

RECOMMENDATIONS:
- ACCEPT: Confidence ≥90, can be auto-applied
- REVIEW: Confidence 50-89, needs human review
- REJECT: Confidence <50, no suitable match found

CRITICAL: Always use exact codes and names from the database above. Be consistent in your mappings.

Respond ONLY with the JSON object, no additional text."""

        return prompt
    
    def get_vaccine_suggestion(self, legacy_vaccine: Dict, candidates: List[Dict] = None) -> Optional[Dict]:
        """
        Get LLM suggestion for vaccine matching.

        Args:
            legacy_vaccine: Legacy vaccine information
            candidates: List of candidate vaccines (optional - LLM will search all)

        Returns:
            LLM response dictionary or None if error
        """
        if not self.client:
            logger.warning("LLM client not initialized. Skipping LLM suggestion.")
            return None
        
        try:
            # If no candidates provided, we'll need the full database
            # This should be passed from the calling function
            if not candidates:
                logger.error("No vaccine database provided to LLM")
                return None

            prompt = self.create_matching_prompt(legacy_vaccine, candidates)
            
            response = self.client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are a medical expert specializing in vaccine identification. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=OPENAI_MAX_TOKENS,
                temperature=OPENAI_TEMPERATURE
            )
            
            # Parse JSON response
            response_text = response.choices[0].message.content.strip()
            
            # Clean up response if it has markdown formatting
            if response_text.startswith('```json'):
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            
            suggestion = json.loads(response_text)
            
            # Validate response structure
            required_keys = ['best_match', 'auto_accept', 'recommendation']
            if not all(key in suggestion for key in required_keys):
                logger.error("LLM response missing required keys")
                return None
            
            logger.info(f"LLM suggestion: {suggestion['recommendation']} "
                       f"(confidence: {suggestion['best_match'].get('confidence', 0)})")
            
            return suggestion
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM JSON response: {str(e)}")
            logger.error(f"Raw response: {response_text}")
            return None
        except Exception as e:
            logger.error(f"Error getting LLM suggestion: {str(e)}")
            return None
    
    def process_llm_suggestion(self, suggestion: Dict, legacy_row_idx: int,
                              required_mapping_df, database: List[Dict]) -> str:
        """
        Process LLM suggestion and update mapping dataframe.

        Args:
            suggestion: LLM suggestion dictionary
            legacy_row_idx: Index of legacy row in mapping dataframe
            required_mapping_df: Required mapping dataframe
            database: Full vaccine database

        Returns:
            Status to set for the mapping
        """
        best_match = suggestion.get('best_match', {})
        confidence = best_match.get('confidence', 0)
        auto_accept = suggestion.get('auto_accept', False)
        recommendation = suggestion.get('recommendation', 'REVIEW')

        # Find the full vaccine information from database
        matched_vaccine = None
        if best_match.get('vaccine_code'):
            for vaccine in database:
                if vaccine['vaccine_code'] == best_match['vaccine_code']:
                    matched_vaccine = vaccine
                    break

        # Use higher threshold for auto-acceptance (90% instead of 85%)
        if matched_vaccine and confidence >= 90 and auto_accept and recommendation == 'ACCEPT':
            # High confidence - auto-apply
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_code']] = \
                str(matched_vaccine['vaccine_code'])
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_name']] = \
                str(matched_vaccine['vaccine_name'])
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['indici_description']] = \
                str(matched_vaccine['long_description'])
            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                f"LLM auto-mapped (confidence: {confidence}%): {best_match.get('rationale', '')}"

            return STATUS_LLM_SUGGESTED

        else:
            # Needs review - store suggestion in comments
            comment_parts = [f"LLM analysis (confidence: {confidence}%)"]

            if best_match.get('vaccine_code'):
                comment_parts.append(f"Suggested: {best_match.get('vaccine_name', 'N/A')} "
                                   f"({best_match.get('vaccine_code', 'N/A')})")

            if best_match.get('rationale'):
                comment_parts.append(f"Rationale: {best_match.get('rationale', '')}")

            # Add alternative matches
            alternatives = suggestion.get('alternative_matches', [])
            if alternatives:
                alt_text = "; ".join([f"{alt.get('vaccine_name', 'N/A')} "
                                    f"({alt.get('confidence', 0)}%)"
                                    for alt in alternatives[:2]])
                comment_parts.append(f"Alternatives: {alt_text}")

            required_mapping_df.loc[legacy_row_idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                " | ".join(comment_parts)

            return STATUS_NEEDS_REVIEW
    
    def batch_process_unmapped(self, vaccine_mapper) -> int:
        """
        Process all unmapped vaccines through LLM with full database access.

        Args:
            vaccine_mapper: VaccineMapper instance

        Returns:
            Number of vaccines processed
        """
        if not self.client:
            logger.warning("LLM client not available for batch processing")
            return 0

        # Prepare full database for LLM
        indici_database = []
        for _, row in vaccine_mapper.indici_df.iterrows():
            vaccine_record = {
                'vaccine_code': row[INDICI_COLUMNS['vaccine_code']],
                'vaccine_name': row[INDICI_COLUMNS['vaccine_name']],
                'brand': row[INDICI_COLUMNS['brand']],
                'long_description': row[INDICI_COLUMNS['long_description']]
            }
            indici_database.append(vaccine_record)

        # Get unmapped rows
        unmapped_mask = (vaccine_mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') | \
                       (vaccine_mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())

        processed_count = 0

        for idx, row in vaccine_mapper.required_mapping_df[unmapped_mask].iterrows():
            legacy_vaccine = {
                'code': row[REQUIRED_MAPPING_COLUMNS['legacy_code']],
                'description': row[REQUIRED_MAPPING_COLUMNS['legacy_description']]
            }

            # Get LLM suggestion with full database
            suggestion = self.get_vaccine_suggestion(legacy_vaccine, indici_database)

            if suggestion:
                # Process the suggestion
                status = self.process_llm_suggestion(
                    suggestion, idx, vaccine_mapper.required_mapping_df, indici_database
                )
                vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = status
                processed_count += 1
            else:
                # LLM failed - mark for review
                vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['mapping_status']] = STATUS_NEEDS_REVIEW
                vaccine_mapper.required_mapping_df.loc[idx, REQUIRED_MAPPING_COLUMNS['comments']] = \
                    "LLM processing failed - manual review required"

        logger.info(f"LLM processed {processed_count} unmapped vaccines")
        return processed_count
