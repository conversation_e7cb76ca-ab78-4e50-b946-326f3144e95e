#!/usr/bin/env python3
"""
Test LLM functionality with your API key.
"""

from llm_assistant import LLMVaccineAssistant
from vaccine_mapper import VaccineMapper
from config import *

def test_llm_connection():
    """Test if LLM connection works."""
    print("Testing LLM connection...")
    
    llm = LLMVaccineAssistant()
    
    if not llm.client:
        print("❌ LLM client not initialized")
        return False
    
    # Test with a simple vaccine mapping
    legacy_vaccine = {
        'code': 'MMR',
        'description': 'Measles Mumps Rubella vaccine'
    }
    
    candidates = [
        {
            'vaccine_code': 'MMR001',
            'vaccine_name': 'Measles Mumps Rubella',
            'brand': 'M-M-R II',
            'long_description': 'Live attenuated measles, mumps, and rubella vaccine',
            'similarity_score': 85.0
        }
    ]
    
    print("Testing LLM suggestion...")
    suggestion = llm.get_vaccine_suggestion(legacy_vaccine, candidates)
    
    if suggestion:
        print("✅ LLM connection successful!")
        print(f"Suggestion: {suggestion}")
        return True
    else:
        print("❌ LLM suggestion failed")
        return False

def test_candidate_generation():
    """Test candidate generation for unmapped vaccines."""
    print("\nTesting candidate generation...")
    
    mapper = VaccineMapper(EXCEL_FILE)
    mapper.load_data()
    mapper.clean_and_normalize_data()
    mapper.initialize_required_mapping()
    
    # Get first unmapped vaccine
    unmapped_mask = (mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']] == '') | \
                   (mapper.required_mapping_df[REQUIRED_MAPPING_COLUMNS['mapping_status']].isna())
    
    if unmapped_mask.any():
        first_unmapped = mapper.required_mapping_df[unmapped_mask].iloc[0]
        legacy_desc = first_unmapped['legacy_description_normalized']
        
        print(f"Testing with: {legacy_desc}")
        
        candidates = mapper.get_candidates_for_llm(legacy_desc)
        print(f"Found {len(candidates)} candidates")
        
        if candidates:
            print("Top candidates:")
            for i, candidate in enumerate(candidates[:3], 1):
                print(f"  {i}. {candidate['vaccine_name']} (score: {candidate['similarity_score']:.1f})")
            return True
        else:
            print("❌ No candidates found")
            return False
    else:
        print("❌ No unmapped vaccines found")
        return False

def main():
    """Run tests."""
    print("🧪 Testing LLM Integration")
    print("=" * 40)
    
    # Test 1: LLM connection
    llm_works = test_llm_connection()
    
    # Test 2: Candidate generation
    candidates_work = test_candidate_generation()
    
    print("\n" + "=" * 40)
    if llm_works and candidates_work:
        print("🎉 All tests passed! LLM should work for mapping.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
